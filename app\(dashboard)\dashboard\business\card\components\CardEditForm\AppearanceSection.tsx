"use client";

import { UseFormReturn } from "react-hook-form";
import { BusinessCardData } from "../../schema";
import { Palette, Info, RotateCcw } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

interface AppearanceSectionProps {
  form: UseFormReturn<BusinessCardData>;
  currentUserPlan: "basic" | "growth" | "pro" | "enterprise" | "trial" | null;
}

export default function AppearanceSection({
  form,
  currentUserPlan,
}: AppearanceSectionProps) {
  // Show this section for all users
  // No longer restricting based on plan

  return (
    <div className="rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-6 mb-4 md:mb-6 transition-all duration-300 hover:shadow-lg">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800">
        <div className="p-2 rounded-lg bg-primary/10 text-primary self-start">
          <Palette className="w-4 sm:w-5 h-4 sm:h-5" />
        </div>
        <div className="flex-1">
          <h3 className="text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100">
            Appearance
          </h3>
          <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5">
            Customize your card&apos;s visual appearance
          </p>
        </div>
        {/* Removed Growth+ badge as these features are now available to all users */}
      </div>

      <div className="flex flex-col gap-4 sm:gap-6">
        {/* Theme Color - Visible to all but only editable for Pro and Enterprise */}
        <FormField
          control={form.control}
          name="theme_color"
          render={({ field }) => (
            <FormItem className="space-y-1 sm:space-y-2">
              <FormLabel className="text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5">
                <Palette className="h-3.5 w-3.5 text-primary" />
                Theme Color
              </FormLabel>
              <FormControl>
                <div className="flex items-center gap-2 sm:gap-3">
                  <div className="relative flex-grow">
                    <Palette className="absolute left-3 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-neutral-500 dark:text-neutral-400" />
                    <Input
                      type="text"
                      {...field}
                      className={`pl-10 w-full font-mono uppercase rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200 ${
                        !(
                          currentUserPlan === "pro" ||
                          currentUserPlan === "enterprise"
                        )
                          ? "opacity-60 cursor-not-allowed"
                          : ""
                      }`}
                      value={field.value || "#F5D76E"}
                      onChange={(e) => {
                        // Only allow changes for Pro and Enterprise users
                        if (
                          currentUserPlan === "pro" ||
                          currentUserPlan === "enterprise"
                        ) {
                          const value = e.target.value;
                          if (
                            value === "" ||
                            /^#([A-Fa-f0-9]{3}|[A-Fa-f0-9]{6})$/.test(value)
                          ) {
                            field.onChange(value);
                          }
                        }
                      }}
                      disabled={
                        !(
                          currentUserPlan === "pro" ||
                          currentUserPlan === "enterprise"
                        )
                      }
                    />
                  </div>
                  <Input
                    type="color"
                    {...field}
                    className={`h-8 sm:h-10 w-10 sm:w-12 p-1 rounded-md ${
                      currentUserPlan === "pro" ||
                      currentUserPlan === "enterprise"
                        ? "cursor-pointer"
                        : "cursor-not-allowed opacity-60"
                    } border border-neutral-200 dark:border-neutral-700 hover:border-primary dark:hover:border-primary transition-all duration-200`}
                    value={field.value || "#F5D76E"}
                    disabled={
                      !(
                        currentUserPlan === "pro" ||
                        currentUserPlan === "enterprise"
                      )
                    }
                  />
                  <div
                    className="h-8 sm:h-10 w-8 sm:w-10 rounded-md border border-neutral-200 dark:border-neutral-700 shadow-inner transition-transform hover:scale-105"
                    style={{ backgroundColor: field.value || "#F5D76E" }}
                  />
                  {/* Reset Button */}
                  {(currentUserPlan === "pro" || currentUserPlan === "enterprise") && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => field.onChange("")}
                      className="h-8 sm:h-10 px-2 sm:px-3 text-xs hover:bg-red-50 hover:border-red-200 hover:text-red-600 dark:hover:bg-red-950/20 dark:hover:border-red-800 dark:hover:text-red-400 transition-colors"
                      title="Reset to default color"
                    >
                      <RotateCcw className="h-3 w-3 sm:h-3.5 sm:w-3.5" />
                    </Button>
                  )}
                </div>
              </FormControl>
              <FormDescription className="text-xs text-neutral-500 dark:text-neutral-400 flex items-center gap-1 ml-1">
                <Info className="w-3 h-3" />
                {currentUserPlan === "pro" || currentUserPlan === "enterprise"
                  ? "Select the primary accent color for your card"
                  : "Theme customization is only available for Pro and Enterprise plans"}
              </FormDescription>
              <FormMessage className="text-xs text-red-500" />
            </FormItem>
          )}
        />

        {/* Card Texture section removed as it doesn't exist in the database */}
      </div>

      {/* Tip Section for Basic and Growth Plan */}
      {(currentUserPlan === "basic" ||
        currentUserPlan === "growth" ||
        currentUserPlan === "trial") && (
        <div className="mt-4 sm:mt-6 rounded-lg bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/20 p-3 sm:p-4 border border-blue-100 dark:border-blue-900/30 shadow-sm">
          <div className="flex items-start gap-2 sm:gap-3">
            <div className="p-1.5 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 mt-0.5 shadow-sm">
              <Info className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
            </div>
            <div>
              <p className="text-xs sm:text-sm font-medium text-blue-800 dark:text-blue-300">
                Pro Plan Unlocks Custom Theme Colors
              </p>
              <p className="text-xs text-blue-700 dark:text-blue-400 mt-0.5 sm:mt-1 leading-relaxed">
                Upgrade to Pro for more customization options, including custom
                theme colors for your card.
              </p>
            </div>
            <button className="ml-auto px-3 sm:px-4 py-1.5 sm:py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-xs sm:text-sm font-medium transition-colors">
              Upgrade
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
