"use client";

import { RefObject } from "react";
import { toast } from "sonner";
import { Download, FileDown, QrCode, CreditCard } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { BusinessCardData } from "../../schema";
import { generateAndDownloadQRCode, downloadRawQRImage } from "@/lib/qrCodeGenerator";
import { downloadBusinessCard, findBusinessCardElement } from "@/lib/cardDownloader";

interface DownloadButtonProps {
  cardData: BusinessCardData;
  cardPreviewRef: RefObject<HTMLDivElement | null>;
}

export default function DownloadButton({
  cardData,
  cardPreviewRef,
}: DownloadButtonProps) {
  // Get QR code SVG element
  const getQRCodeElement = () => {
    const slug = cardData.business_slug;
    if (!slug) {
      toast.error("Please set a business slug first.");
      return null;
    }

    // Check if ref and current exist before querying
    if (!cardPreviewRef?.current) {
      toast.error("Preview element not found.");
      return null;
    }

    const qrCodeElement = cardPreviewRef.current.querySelector(
      "#business-card-qrcode > svg"
    );

    if (!(qrCodeElement instanceof SVGSVGElement)) {
      toast.error("QR code SVG element not found.");
      return null;
    }

    return qrCodeElement;
  };

  // Download A4 formatted QR code
  const handleDownloadA4QR = async () => {
    const qrCodeElement = getQRCodeElement();
    if (!qrCodeElement) return;

    try {
      // Format the address for display
      const addressParts = [
        cardData.address_line,
        cardData.locality,
        cardData.city,
        cardData.state,
        cardData.pincode,
      ].filter(Boolean);

      const formattedAddress =
        addressParts.join(", ") || "Address not available";
      const formattedOwnerName = cardData.member_name?.trim() || "Owner";
      const slug = cardData.business_slug || "";

      await generateAndDownloadQRCode(qrCodeElement, {
        businessName: cardData.business_name || "Business",
        ownerName: formattedOwnerName,
        address: formattedAddress,
        slug,
        qrValue: `https://dukancard.in/${slug}`,
        themeColor: cardData.theme_color || "#F59E0B",
      });

      toast.success("A4 QR code downloaded!");
    } catch (error) {
      console.error("Error generating QR code:", error);
      toast.error("Could not download QR code.");
    }
  };

  // Download raw QR image
  const handleDownloadRawQR = async () => {
    const qrCodeElement = getQRCodeElement();
    if (!qrCodeElement) return;

    try {
      const slug = cardData.business_slug || "";
      await downloadRawQRImage(qrCodeElement, slug);
      toast.success("High-quality QR image downloaded!");
    } catch (error) {
      console.error("Error downloading QR image:", error);
      toast.error("Could not download QR image.");
    }
  };

  // Download business card as PNG
  const handleDownloadCardPNG = async () => {
    const slug = cardData.business_slug;
    if (!slug) {
      toast.error("Please set a business slug first.");
      return;
    }



    try {
      // Find the business card element using the ref or fallback to improved global search
      let cardElement: HTMLElement | null = null;

      if (cardPreviewRef?.current) {
        // Look for the business card within the ref container first
        const cardInContainer = cardPreviewRef.current.querySelector('[data-card-element]') as HTMLElement;
        if (cardInContainer) {
          cardElement = cardInContainer;
        } else {
          // Try other selectors within the dashboard container
          const fallbackSelectors = [
            '.business-card-preview',
            '[class*="business-card"]',
            '[class*="card-preview"]'
          ];

          for (const selector of fallbackSelectors) {
            const element = cardPreviewRef.current.querySelector(selector) as HTMLElement;
            if (element) {
              cardElement = element;
              break;
            }
          }
        }
      }

      // If ref approach fails, use the improved global search which prioritizes authenticated cards
      if (!cardElement) {
        cardElement = findBusinessCardElement();
      }

      // Additional validation to ensure we have the right element
      if (cardElement) {
        const rect = cardElement.getBoundingClientRect();

        // If the element is too large, it might be a container, try to find the actual card
        if (rect.width > 500) {
          const actualCard = cardElement.querySelector('[data-card-element]') as HTMLElement;
          if (actualCard) {
            cardElement = actualCard;
          }
        }
      }

      if (!cardElement) {
        toast.error("Business card not found for download.");
        return;
      }



      await downloadBusinessCard(cardElement, {
        businessName: cardData.business_name || "Business",
        businessSlug: slug,
      });

      toast.success("Digital card downloaded as PNG!");
    } catch (error) {
      console.error("Error downloading business card as PNG:", error);
      toast.error("Could not download business card.");
    }
  };



  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          disabled={!cardData.business_slug}
          className="w-full flex items-center gap-2 border-border hover:bg-accent"
        >
          <Download className="h-4 w-4" /> Download Options
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuItem onClick={handleDownloadCardPNG} className="cursor-pointer">
          <CreditCard className="mr-2 h-4 w-4" />
          <span>Download Digital Card (PNG)</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleDownloadA4QR} className="cursor-pointer">
          <FileDown className="mr-2 h-4 w-4" />
          <span>Download A4 Size QR</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleDownloadRawQR} className="cursor-pointer">
          <QrCode className="mr-2 h-4 w-4" />
          <span>Download High-Quality QR Image</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
