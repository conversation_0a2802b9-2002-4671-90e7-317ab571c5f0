#!/usr/bin/env node

/**
 * Business Setup Diagnostic Script
 * 
 * This script diagnoses business setup issues for subscription testing:
 * - Checks if business profile exists
 * - Verifies payment_subscriptions row exists
 * - Tests API endpoint connectivity
 * - Validates test environment setup
 */

const http = require('http');
const readline = require('readline');

// Configuration
const BASE_URL = 'http://localhost:3000';

/**
 * Make HTTP request
 */
function makeRequest(url, timeout = 15000) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    
    const req = http.request({
      hostname: urlObj.hostname,
      port: urlObj.port || 80,
      path: urlObj.pathname + urlObj.search,
      method: 'GET',
      timeout: timeout
    }, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          resolve({ status: res.statusCode, data: result });
        } catch (_error) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', (error) => {
      if (error.code === 'ECONNREFUSED') {
        reject(new Error('Development server not running. Start with: npm run dev'));
      } else {
        reject(error);
      }
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
}

/**
 * Get business ID from user
 */
function getBusinessIdFromUser() {
  return new Promise((resolve) => {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    console.log('🔍 Business Setup Diagnostic Tool');
    console.log('═'.repeat(40));
    console.log('This tool will check if a business is properly set up for subscription testing.\n');
    
    rl.question('Enter business profile ID to diagnose: ', (businessId) => {
      rl.close();
      
      if (!businessId || businessId.trim().length === 0) {
        console.log('❌ Business ID is required. Exiting...');
        process.exit(1);
      }
      
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(businessId.trim())) {
        console.log('❌ Invalid business ID format. Exiting...');
        process.exit(1);
      }
      
      console.log(`✅ Diagnosing business ID: ${businessId.trim()}\n`);
      resolve(businessId.trim());
    });
  });
}

/**
 * Test API endpoint connectivity
 */
async function testAPIConnectivity() {
  console.log('🌐 Testing API Connectivity...');
  console.log('─'.repeat(30));
  
  try {
    // Test basic endpoint
    const response = await makeRequest(`${BASE_URL}/api/test/subscription-flow?bypass=internal-testing`);
    
    if (response.status === 200) {
      console.log('✅ API endpoint accessible');
      console.log(`   Status: ${response.status}`);
      
      if (response.data && response.data.success) {
        console.log('✅ API responding correctly');
        return true;
      } else {
        console.log('⚠️  API accessible but not responding correctly');
        console.log(`   Response: ${JSON.stringify(response.data).substring(0, 100)}...`);
        return false;
      }
    } else {
      console.log(`❌ API endpoint error: ${response.status}`);
      console.log(`   Response: ${JSON.stringify(response.data).substring(0, 100)}...`);
      return false;
    }
  } catch (error) {
    console.log(`❌ API connectivity failed: ${error.message}`);
    return false;
  }
}

/**
 * Test specific business scenario endpoints
 */
async function testBusinessEndpoints(businessId) {
  console.log('🧪 Testing Business-Specific Endpoints...');
  console.log('─'.repeat(40));
  
  const endpoints = [
    {
      name: 'Subscription Flow Tests',
      url: `${BASE_URL}/api/test/subscription-flow?type=trial-auth&bypass=internal-testing&businessId=${businessId}`
    },
    {
      name: 'Subscription Scenarios',
      url: `${BASE_URL}/api/test/subscription-scenarios?type=trial-auth&bypass=internal-testing&businessId=${businessId}`
    }
  ];
  
  const results = [];
  
  for (const endpoint of endpoints) {
    console.log(`📋 Testing: ${endpoint.name}`);
    
    try {
      const response = await makeRequest(endpoint.url);
      
      if (response.status === 200) {
        console.log(`✅ ${endpoint.name}: Accessible`);
        
        if (response.data && response.data.success) {
          console.log(`   Response: Success`);
          
          if (response.data.data) {
            if (Array.isArray(response.data.data)) {
              console.log(`   Data: Array with ${response.data.data.length} items`);
              if (response.data.data.length > 0) {
                const firstItem = response.data.data[0];
                console.log(`   First item success: ${firstItem.success || 'unknown'}`);
                if (firstItem.message) {
                  console.log(`   Message: ${firstItem.message}`);
                }
              }
            } else {
              console.log(`   Data: Object with keys: ${Object.keys(response.data.data).join(', ')}`);
            }
          } else {
            console.log(`   ⚠️  No data in response`);
          }
          
          results.push({ endpoint: endpoint.name, success: true, hasData: !!response.data.data });
        } else {
          console.log(`   ❌ Response indicates failure`);
          console.log(`   Error: ${response.data.error || 'Unknown error'}`);
          results.push({ endpoint: endpoint.name, success: false, error: response.data.error });
        }
      } else {
        console.log(`❌ ${endpoint.name}: HTTP ${response.status}`);
        results.push({ endpoint: endpoint.name, success: false, status: response.status });
      }
    } catch (error) {
      console.log(`❌ ${endpoint.name}: ${error.message}`);
      results.push({ endpoint: endpoint.name, success: false, error: error.message });
    }
    
    console.log('');
  }
  
  return results;
}

/**
 * Run comprehensive diagnosis
 */
async function runDiagnosis() {
  console.log('🔍 Starting Business Setup Diagnosis...\n');
  
  const businessId = await getBusinessIdFromUser();
  
  // Test API connectivity
  const apiWorking = await testAPIConnectivity();
  console.log('');
  
  if (!apiWorking) {
    console.log('❌ API connectivity issues detected. Cannot proceed with business-specific tests.');
    console.log('💡 Make sure the development server is running: npm run dev');
    process.exit(1);
  }
  
  // Test business-specific endpoints
  const endpointResults = await testBusinessEndpoints(businessId);
  
  // Generate diagnosis summary
  console.log('📊 Diagnosis Summary:');
  console.log('═'.repeat(40));
  console.log(`   Business ID: ${businessId}`);
  console.log(`   API Connectivity: ${apiWorking ? '✅ Working' : '❌ Failed'}`);
  
  const workingEndpoints = endpointResults.filter(r => r.success).length;
  const totalEndpoints = endpointResults.length;
  console.log(`   Endpoints: ${workingEndpoints}/${totalEndpoints} working`);
  
  const endpointsWithData = endpointResults.filter(r => r.hasData).length;
  console.log(`   Endpoints with data: ${endpointsWithData}/${totalEndpoints}`);
  
  // Recommendations
  console.log('\n💡 Recommendations:');
  console.log('─'.repeat(20));
  
  if (endpointsWithData === 0) {
    console.log('❌ No endpoints returned data for this business ID.');
    console.log('   Possible issues:');
    console.log('   1. Business profile does not exist in database');
    console.log('   2. payment_subscriptions row missing for this business');
    console.log('   3. Business ID format is correct but business not found');
    console.log('');
    console.log('   Solutions:');
    console.log('   1. Verify the business ID exists in business_profiles table');
    console.log('   2. Ensure payment_subscriptions row exists for this business');
    console.log('   3. Try with a different business ID that has complete setup');
  } else if (endpointsWithData < totalEndpoints) {
    console.log('⚠️  Some endpoints working, others not.');
    console.log('   This suggests partial setup or specific endpoint issues.');
  } else {
    console.log('✅ All endpoints working correctly!');
    console.log('   Business is properly set up for subscription testing.');
  }
}

// Run diagnosis if this script is executed directly
if (require.main === module) {
  runDiagnosis().catch(error => {
    console.error('❌ Diagnosis failed:', error);
    process.exit(1);
  });
}

module.exports = { runDiagnosis, testAPIConnectivity, testBusinessEndpoints };
