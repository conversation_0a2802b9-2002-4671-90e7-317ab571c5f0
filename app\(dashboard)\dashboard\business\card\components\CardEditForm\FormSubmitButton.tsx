"use client";

import { Loader2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { UseFormReturn } from "react-hook-form";
import { BusinessCardData } from "../../schema"; // Added import

interface FormSubmitButtonProps {
  form: UseFormReturn<BusinessCardData>; // Changed 'any' to 'BusinessCardData'
  isPending: boolean;
  isLogoUploading: boolean;
  isCheckingSlug?: boolean;
  isPincodeLoading?: boolean;
  onSave: () => void;
}

export default function FormSubmitButton({
  form,
  isPending,
  isLogoUploading,
  isCheckingSlug = false,
  isPincodeLoading = false,
  onSave,
}: FormSubmitButtonProps) {
  return (
    <div className="mt-8 flex flex-col space-y-4">
      <div className="h-px w-full bg-neutral-200 dark:bg-neutral-800"></div>
      <div className="flex justify-between items-center">
        <div className="text-sm text-neutral-500 dark:text-neutral-400">
          {form.formState.isDirty ? (
            <span className="flex items-center text-amber-600 dark:text-amber-400">
              <span className="relative flex h-2 w-2 mr-2">
                <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-amber-400 opacity-75"></span>
                <span className="relative inline-flex rounded-full h-2 w-2 bg-amber-500"></span>
              </span>
              You have unsaved changes
            </span>
          ) : (
            <span className="flex items-center">
              <span className="relative flex h-2 w-2 mr-2">
                <span className="relative inline-flex rounded-full h-2 w-2 bg-green-500"></span>
              </span>
              All changes saved
            </span>
          )}
        </div>
        <Button
          type="button"
          disabled={
            isPending ||
            isLogoUploading ||
            isCheckingSlug ||
            isPincodeLoading ||
            Object.keys(form.formState.errors).length > 0
          }
          onClick={onSave}
          className="bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] disabled:opacity-50 transition-all duration-200 ease-in-out shadow-sm hover:shadow-md"
        >
          {(isPending || isLogoUploading || isCheckingSlug || isPincodeLoading) ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : null}
          {isPending ? "Saving..." :
           isLogoUploading ? "Uploading..." :
           isCheckingSlug ? "Checking URL..." :
           isPincodeLoading ? "Loading Location..." :
           "Save Changes"}
        </Button>
      </div>
    </div>
  );
}
