"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { CreditC<PERSON>, Sparkles } from "lucide-react";
import { useRouter } from "next/navigation";

import { ModernTabs, ModernTabsContent } from "./subscription-manager/ModernTabs";
import SubscriptionProcessingIndicator from "./subscription-manager/SubscriptionProcessingIndicator";
import EnhancedPlanSelectionSection from "./EnhancedPlanSelectionSection";
import EnhancedTrialAlert from "./EnhancedTrialAlert";
import SubscriptionTabContent from "./SubscriptionTabContent";
import DialogManager from "./DialogManager";

import { useSubscriptionLogic } from "../hooks/useSubscriptionLogic";
import { useSubscriptionHandler } from "../hooks/useSubscriptionHandler";
import { useUrlParameterHandler } from "../hooks/useUrlParameterHandler";
import { useTrialManagement } from "./TrialManagement";

import { PricingPlan } from "@/lib/PricingPlans";
import { SubscriptionStatus } from "../page";

interface PlanPageContainerProps {
  userId: string;
  currentPlanDetails?: PricingPlan;
  subscriptionStatus: SubscriptionStatus;
  trialEndDate: string | null;
  subscriptionEndDate: string | null;
  monthlyPlans: PricingPlan[];
  yearlyPlans: PricingPlan[];
  currentSubscriptionId: string | null;
  nextBillingDate: string | null;
  cancellationRequestedAt: string | null;
  cancelledAt: string | null;
  planCycle: "monthly" | "yearly";
  authenticatedSubscriptionStartDate?: string | null;
  subscriptionStartDate?: string | null;
  subscriptionExpiryTime?: string | null;
  subscriptionChargeTime?: string | null;
  isEligibleForRefund?: boolean;
  lastPaymentMethod?: string | null;
  razorpaySubscriptionId?: string | null;
}

export default function PlanPageContainer(props: PlanPageContainerProps) {
  const router = useRouter();

  const {
    userId,
    currentPlanDetails,
    subscriptionStatus,
    trialEndDate,
    monthlyPlans,
    yearlyPlans,
    currentSubscriptionId,
    nextBillingDate,
    cancellationRequestedAt,
    cancelledAt,
    planCycle: currentPlanCycle,
    subscriptionStartDate,
    subscriptionExpiryTime,
    subscriptionChargeTime,
    isEligibleForRefund: propIsEligibleForRefund,
    lastPaymentMethod,
    razorpaySubscriptionId,
  } = props;

  const [activeTab, setActiveTab] = useState<"plans" | "subscription">("subscription");

  // Check if user is eligible for free trial (trial_end_date is null)
  const isEligibleForFreeTrial = trialEndDate === null;

  // Check if user is eligible for refund
  const isEligibleForRefund = () => {
    if (propIsEligibleForRefund !== undefined) {
      return propIsEligibleForRefund;
    }
    return false;
  };

  // Use subscription logic hook
  const subscriptionLogic = useSubscriptionLogic({
    currentSubscriptionId,
    subscriptionStatus,
    currentPlanDetails,
    currentPlanCycle,
    lastPaymentMethod,
    razorpaySubscriptionId,
    isEligibleForFreeTrial,
  });

  // Use subscription handler hook
  const { handleDialogSubscribe } = useSubscriptionHandler({
    currentSubscriptionId,
    subscriptionStatus,
    currentPlanDetails,
    currentPlanCycle,
    lastPaymentMethod,
    razorpaySubscriptionId,
    trialEndDate,
    dialogPlan: subscriptionLogic.dialogPlan,
    billingCycle: subscriptionLogic.billingCycle,
    setDialogLoading: subscriptionLogic.setDialogLoading,
    setIsPlanDialogOpen: subscriptionLogic.setIsPlanDialogOpen,
    // Removed UPI warning dialog props as they're no longer needed
    startProcessing: subscriptionLogic.startProcessing,
    completeProcessing: subscriptionLogic.completeProcessing,
    resetProcessing: subscriptionLogic.resetProcessing,
    setSubscriptionCreated: subscriptionLogic.setSubscriptionCreated,
    setFuturePaymentAuthorized: subscriptionLogic.setFuturePaymentAuthorized,
    validateSubscriptionRequest: subscriptionLogic.validateSubscriptionRequest,
    handleFreePlanSubscription: subscriptionLogic.handleFreePlanSubscription,
    determineSubscriptionFlow: subscriptionLogic.determineSubscriptionFlow,
    setActiveTab,
  });

  // Use trial management hook
  const { handleActivateTrial } = useTrialManagement({
    dialogPlan: subscriptionLogic.dialogPlan,
    billingCycle: subscriptionLogic.billingCycle,
    setDialogLoading: subscriptionLogic.setDialogLoading,
    setIsFirstTimePaidPlanDialogOpen: subscriptionLogic.setIsFirstTimePaidPlanDialogOpen,
    setActiveTab,
    startProcessing: subscriptionLogic.startProcessing,
    completeProcessing: subscriptionLogic.completeProcessing,
    setSubscriptionCreated: subscriptionLogic.setSubscriptionCreated,
  });

  // Handle URL parameters
  useUrlParameterHandler({ setActiveTab });

  // Removed UPI warning dialog safety check as it's no longer needed

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value as "plans" | "subscription");
  };

  // Plans for current billing cycle
  const plans = subscriptionLogic.billingCycle === "monthly" ? monthlyPlans : yearlyPlans;

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.1,
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24,
        duration: 0.5,
      },
    },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-6 max-w-7xl mx-auto"
    >
      {/* Trial Alert */}
      {trialEndDate && new Date(trialEndDate) > new Date() && (
        <motion.div variants={itemVariants}>
          <EnhancedTrialAlert trialEndDate={trialEndDate} />
        </motion.div>
      )}

      <motion.div variants={itemVariants}>
        <div className="rounded-xl border bg-white dark:bg-black backdrop-blur-sm shadow-lg transition-all duration-300 relative overflow-hidden p-4 sm:p-5 md:p-6">
          <ModernTabs
            tabs={[
              {
                id: "subscription",
                label: "My Subscription",
                icon: <Sparkles className="h-4 w-4" />
              },
              {
                id: "plans",
                label: "Plans",
                icon: <CreditCard className="h-4 w-4" />
              }
            ]}
            value={activeTab}
            onChange={handleTabChange}
            className="w-full"
            indicatorLayoutId="mainPageTabs"
          >
            <ModernTabsContent value="plans" className="space-y-6">
              <EnhancedPlanSelectionSection
                subscriptionStatus={subscriptionStatus}
                billingCycle={subscriptionLogic.billingCycle}
                setBillingCycle={subscriptionLogic.setBillingCycle}
                plans={plans}
                currentPlanId={currentPlanDetails?.id}
                currentPlanCycle={currentPlanCycle}
                loadingStates={{}}
                onPlanAction={subscriptionLogic.handlePlanAction}
              />
            </ModernTabsContent>

            <ModernTabsContent value="subscription" className="space-y-6">
              <SubscriptionTabContent
                userId={userId}
                currentSubscriptionId={currentSubscriptionId}
                subscriptionStatus={subscriptionStatus}
                currentPlanDetails={currentPlanDetails}
                currentPlanCycle={currentPlanCycle}
                nextBillingDate={nextBillingDate}
                subscriptionStartDate={subscriptionStartDate}
                subscriptionExpiryTime={subscriptionExpiryTime}
                subscriptionChargeTime={subscriptionChargeTime}
                lastPaymentMethod={lastPaymentMethod}
                cancellationRequestedAt={cancellationRequestedAt}
                cancelledAt={cancelledAt}
                trialEndDate={trialEndDate}
                isEligibleForRefund={isEligibleForRefund()}
                setActiveTab={setActiveTab}
              />
            </ModernTabsContent>
          </ModernTabs>
        </div>
      </motion.div>

      {/* Dialog Manager */}
      <DialogManager
        dialogPlan={subscriptionLogic.dialogPlan}
        isPlanDialogOpen={subscriptionLogic.isPlanDialogOpen}
        isFirstTimePaidPlanDialogOpen={subscriptionLogic.isFirstTimePaidPlanDialogOpen}
        // Removed UPI warning dialog props as they're no longer needed
        dialogLoading={subscriptionLogic.dialogLoading}
        billingCycle={subscriptionLogic.billingCycle}
        subscriptionStatus={subscriptionStatus}
        trialEndDate={trialEndDate}
        lastPaymentMethod={lastPaymentMethod}
        setIsPlanDialogOpen={subscriptionLogic.setIsPlanDialogOpen}
        setIsFirstTimePaidPlanDialogOpen={subscriptionLogic.setIsFirstTimePaidPlanDialogOpen}
        // Removed UPI warning dialog setters
        setDialogLoading={subscriptionLogic.setDialogLoading}
        setActiveTab={setActiveTab}
        handleDialogSubscribe={handleDialogSubscribe}
        handleActivateTrial={handleActivateTrial}
        resetProcessing={subscriptionLogic.resetProcessing}
        startProcessing={subscriptionLogic.startProcessing}
        completeProcessing={subscriptionLogic.completeProcessing}
        setSubscriptionCreated={subscriptionLogic.setSubscriptionCreated}
        setFuturePaymentAuthorized={subscriptionLogic.setFuturePaymentAuthorized}
        router={router}
      />

      <SubscriptionProcessingIndicator />
    </motion.div>
  );
}
