/**
 * CENTRALIZED SUBSCRIPTION API ENDPOINT
 * 
 * This endpoint provides a single entry point for all subscription operations
 * using the centralized SubscriptionFlowManager logic.
 */

import { NextRequest, NextResponse } from "next/server";
import { processSubscription } from "@/lib/actions/subscription/centralized";

// Simple in-memory cache for request deduplication
// In production, you might want to use Redis or a database
const requestCache = new Map<string, { timestamp: number; result: any }>();
const CACHE_DURATION = 30000; // 30 seconds

// Clean up old cache entries
function cleanupCache() {
  const now = Date.now();
  for (const [key, value] of requestCache.entries()) {
    if (now - value.timestamp > CACHE_DURATION) {
      requestCache.delete(key);
    }
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get request ID from headers for deduplication
    const requestId = request.headers.get('X-Request-ID');

    // Clean up old cache entries
    cleanupCache();

    // Check if this request has been processed recently
    if (requestId && requestCache.has(requestId)) {
      const cached = requestCache.get(requestId);
      console.log(`[CENTRALIZED_API] Returning cached result for request ID: ${requestId}`);
      return NextResponse.json(cached!.result);
    }

    // Parse request body
    const body = await request.json();
    const { planId, planCycle } = body;

    console.log(`[CENTRALIZED_API] Processing new request:`, {
      requestId,
      planId,
      planCycle,
      timestamp: new Date().toISOString()
    });

    // Validate required parameters
    if (!planId || !planCycle) {
      return NextResponse.json(
        {
          success: false,
          error: "Missing required parameters: planId and planCycle"
        },
        { status: 400 }
      );
    }

    // Validate plan cycle
    if (!['monthly', 'yearly'].includes(planCycle)) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid plan cycle. Must be 'monthly' or 'yearly'"
        },
        { status: 400 }
      );
    }

    // Process subscription using centralized logic
    const result = await processSubscription(planId, planCycle);

    // Prepare response
    let response;
    if (result.success) {
      response = {
        success: true,
        message: result.message,
        data: {
          subscription_id: result.subscriptionId,
          payment_required: result.paymentRequired,
          ...result.data
        }
      };
    } else {
      response = {
        success: false,
        error: result.error || result.message
      };
    }

    // Cache the result if we have a request ID
    if (requestId) {
      requestCache.set(requestId, {
        timestamp: Date.now(),
        result: response
      });
    }

    // Return result
    if (result.success) {
      return NextResponse.json(response);
    } else {
      return NextResponse.json(response, { status: 400 });
    }

  } catch (error) {
    console.error('Error processing subscription:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Internal server error"
      },
      { status: 500 }
    );
  }
}
