[{"id": "45a077af917e988bb473dae23c092270", "filePath": "C:\\Users\\<USER>\\Desktop\\dukancard\\app\\components\\ui\\container.tsx", "content": "import React from \"react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface ContainerProps extends React.HTMLAttributes<HTMLDivElement> {\r\n  children: React.ReactNode;\r\n}\r\n\r\nexport function Container({ \r\n  children, \r\n  className, \r\n  ...props \r\n}: ContainerProps) {\r\n  return (\r\n    <div \r\n      className={cn(\r\n        \"container mx-auto px-4 md:px-6 lg:px-8 max-w-7xl\", \r\n        className\r\n      )} \r\n      {...props}\r\n    >\r\n      {children}\r\n    </div>\r\n  );\r\n}\r\n", "startLine": 0, "endLine": 24, "chunkIndex": 0, "fileHash": "239d8a9855cbcce146b6e44c468e2537", "lastModified": 1748523273858, "language": "typescript", "imports": ["react", "@/lib/utils"]}, {"id": "aa14aaf1fe32ed0d30d707a53d04b6de", "filePath": "C:\\Users\\<USER>\\Desktop\\dukancard\\app\\components\\ui\\test-file.tsx", "content": "// Test file for auto-indexing\nimport React from 'react';\n\nexport const TestComponent = () => {\n  return (\n    <div>\n      <h1>This is a test component for auto-indexing</h1>\n      <p>Testing if the file watcher detects new files</p>\n    </div>\n  );\n};\n\nexport default TestComponent;\n", "startLine": 0, "endLine": 13, "chunkIndex": 0, "fileHash": "9643793269368a3e6022f9711f9acc4f", "lastModified": 1749044145064, "language": "typescript", "imports": ["react"]}, {"id": "a379a77f8123f5db6337d1dca9600e3f", "filePath": "C:\\Users\\<USER>\\Desktop\\dukancard\\app\\components\\ui\\.indexer-metadata.json", "content": "{\n  \"version\": \"2.0.0\",\n  \"totalChunks\": 8,\n  \"totalFiles\": 3,\n  \"lastIndexed\": 1749044344859,\n  \"projectPath\": \"../app/components/ui\",\n  \"fileHashes\": {\n    \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dukancard\\\\app\\\\components\\\\ui\\\\container.tsx\": \"239d8a9855cbcce146b6e44c468e2537\",\n    \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dukancard\\\\app\\\\components\\\\ui\\\\.indexer-metadata.json\": \"e9571753afd9f1c8cfcb18d03451e4f6\",\n    \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dukancard\\\\app\\\\components\\\\ui\\\\test-file.tsx\": \"9643793269368a3e6022f9711f9acc4f\"\n  },\n  \"chunkCount\": 0\n}", "startLine": 0, "endLine": 12, "chunkIndex": 0, "fileHash": "ee22404b28292f920acc5b4802b65b62", "lastModified": 1749044344862, "language": "json"}]