"use client";

import { UseFormReturn } from "react-hook-form";
import { BusinessCardData } from "../../schema";
import { UserPlanStatus } from "../../CardEditorClient";

// Import all the form sections
import BasicInfoSection from "./BasicInfoSection";
import ContactLocationSection from "./ContactLocationSection";
import AppearanceSection from "./AppearanceSection";
import CustomBrandingSection from "./CustomBrandingSection";
import CustomAdUpload from "./CustomAdUpload";
import BusinessDetailsSection from "./BusinessDetailsSection";
import LinksSection from "./LinksSection";
import StatusSlugSection from "./StatusSlugSection";

interface CardEditFormContentProps {
  form: UseFormReturn<BusinessCardData>;
  canGoOnline: boolean;
  currentUserPlan: UserPlanStatus | null;
  onFileSelect: (_file: File | null) => void;
  isPincodeLoading: boolean;
  availableLocalities: string[];
  onPincodeChange: (_pincode: string) => void;
  isLogoUploading: boolean;
  onLogoDelete: () => void;
  isSubscriptionHalted: boolean;
  onSlugCheckingChange?: (_isChecking: boolean) => void;
}

export default function CardEditFormContent({
  form,
  canGoOnline,
  currentUserPlan,
  onFileSelect,
  isPincodeLoading,
  availableLocalities,
  onPincodeChange,
  isLogoUploading,
  onLogoDelete,
  isSubscriptionHalted,
  onSlugCheckingChange: _onSlugCheckingChange,
}: CardEditFormContentProps) {
  return (
    <div className="space-y-8">
      {/* Basic Info Section */}
      <BasicInfoSection
        form={form}
        onFileSelect={onFileSelect}
        isLogoUploading={isLogoUploading}
        onLogoDelete={onLogoDelete}
      />

      {/* Contact & Location Section */}
      <ContactLocationSection
        form={form}
        isPincodeLoading={isPincodeLoading}
        availableLocalities={availableLocalities}
        onPincodeChange={onPincodeChange}
      />

      {/* Appearance Section */}
      <AppearanceSection form={form} currentUserPlan={currentUserPlan} />

      {/* Custom Branding Section - Only for Pro/Enterprise */}
      <CustomBrandingSection form={form} currentUserPlan={currentUserPlan} />

      {/* Custom Ad Upload Section - Only for Pro/Enterprise */}
      <CustomAdUpload form={form} currentUserPlan={currentUserPlan} />

      {/* Business Details Section */}
      <BusinessDetailsSection form={form} />

      {/* Links Section */}
      <LinksSection form={form} />

      {/* Status & Slug Section */}
      <StatusSlugSection
        form={form}
        slugStatus={{ checking: false, available: null, message: null }}
        canGoOnline={canGoOnline}
        isSubscriptionHalted={isSubscriptionHalted}
        onSlugCheckingChange={_onSlugCheckingChange}
      />
    </div>
  );
}
