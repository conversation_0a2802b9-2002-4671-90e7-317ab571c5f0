"use server";

import { createClient } from "@/utils/supabase/server";
import { createAdminClient } from "@/utils/supabase/admin";
import { getThemeSpecificHeaderImagePath } from "@/lib/utils/storage-paths";

export interface ThemeSpecificHeaderUploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

export interface ThemeSpecificHeaderUpdateResult {
  success: boolean;
  error?: string;
}

/**
 * Upload theme-specific custom header image with compression and auto-save to database
 */
export async function uploadThemeSpecificHeaderImage(
  formData: FormData,
  theme: 'light' | 'dark'
): Promise<ThemeSpecificHeaderUploadResult> {
  try {
    // Create admin client for storage operations
    const adminSupabase = createAdminClient();

    // Get authenticated user
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    // Extract the image file from FormData
    const imageFile = formData.get("image") as File;
    if (!imageFile) {
      return {
        success: false,
        error: "No image file provided",
      };
    }

    // Validate file type
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];
    if (!allowedTypes.includes(imageFile.type)) {
      return {
        success: false,
        error: "Invalid file type. Please upload a JPEG, PNG, or WebP image.",
      };
    }

    // Validate file size (5MB limit)
    const maxSizeBytes = 5 * 1024 * 1024; // 5MB
    if (imageFile.size > maxSizeBytes) {
      const fileSizeMB = (imageFile.size / (1024 * 1024)).toFixed(1);
      return {
        success: false,
        error: `Image size (${fileSizeMB}MB) is too large. Please choose an image smaller than 5MB for optimal performance and faster loading.`,
      };
    }

    const bucketName = "business";
    const timestamp = Date.now() + Math.floor(Math.random() * 1000);
    const imagePath = getThemeSpecificHeaderImagePath(user.id, timestamp, theme);

    // File is already compressed on client-side, just upload it
    const fileBuffer = Buffer.from(await imageFile.arrayBuffer());

    // Upload to Supabase Storage using admin client
    const { error: uploadError } = await adminSupabase.storage
      .from(bucketName)
      .upload(imagePath, fileBuffer, {
        contentType: imageFile.type, // Use original file type (already compressed)
        upsert: true
      });

    if (uploadError) {
      console.error("Theme-Specific Header Upload Error:", uploadError);
      return {
        success: false,
        error: `Failed to upload image: ${uploadError.message}`,
      };
    }

    // Get the public URL
    const { data: urlData } = adminSupabase.storage
      .from(bucketName)
      .getPublicUrl(imagePath);

    if (!urlData?.publicUrl) {
      return {
        success: false,
        error: "Could not retrieve public URL after upload.",
      };
    }

    // Auto-save to database - update custom_branding field
    const { data: profile, error: fetchError } = await supabase
      .from("business_profiles")
      .select("custom_branding")
      .eq("id", user.id)
      .single();

    if (fetchError) {
      return {
        success: false,
        error: "Failed to fetch current branding data",
      };
    }

    const currentBranding = profile?.custom_branding || {};
    const fieldName = theme === 'light' ? 'custom_header_image_light_url' : 'custom_header_image_dark_url';

    const { error: updateError } = await supabase
      .from("business_profiles")
      .update({
        custom_branding: {
          ...currentBranding,
          [fieldName]: urlData.publicUrl,
          hide_dukancard_branding: true, // Auto-enable when image is uploaded
        }
      })
      .eq("id", user.id);

    if (updateError) {
      console.error("Database update error:", updateError);
      return {
        success: false,
        error: "Failed to save branding data to database",
      };
    }

    return {
      success: true,
      url: urlData.publicUrl,
    };

  } catch (error) {
    console.error("Theme-specific header upload error:", error);
    return {
      success: false,
      error: "An unexpected error occurred during upload",
    };
  }
}

/**
 * Delete theme-specific custom header image and reset data
 */
export async function deleteThemeSpecificHeaderImage(
  theme: 'light' | 'dark'
): Promise<ThemeSpecificHeaderUpdateResult> {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    // First, get the current custom branding data to extract the image URL
    const { data: profile, error: fetchError } = await supabase
      .from("business_profiles")
      .select("custom_branding")
      .eq("id", user.id)
      .single();

    if (fetchError) {
      return {
        success: false,
        error: "Failed to fetch current branding data",
      };
    }

    const currentBranding = profile?.custom_branding || {};
    const fieldName = theme === 'light' ? 'custom_header_image_light_url' : 'custom_header_image_dark_url';
    const imageUrl = currentBranding[fieldName];

    // Delete from storage if URL exists
    if (imageUrl) {
      try {
        const adminSupabase = createAdminClient();
        const bucketName = "business";

        // Extract the file path from the URL
        const urlParts = imageUrl.split('/');
        const _fileName = urlParts[urlParts.length - 1];
        const userIdFromUrl = urlParts[urlParts.length - 4]; // Assuming URL structure

        if (userIdFromUrl === user.id) {
          // Construct the full path for deletion
          const pathParts = urlParts.slice(-4); // Get last 4 parts: userId/branding/header_theme_timestamp.webp
          const filePath = pathParts.join('/');

          await adminSupabase.storage
            .from(bucketName)
            .remove([filePath]);
        }
      } catch (storageError) {
        console.error("Storage deletion error:", storageError);
        // Continue with database update even if storage deletion fails
      }
    }

    // Update database to remove the image URL
    const updatedBranding = {
      ...currentBranding,
      [fieldName]: "",
    };

    // Check if we should disable hide_dukancard_branding
    const hasAnyImage = !!(
      updatedBranding.custom_header_image_url?.trim() ||
      updatedBranding.custom_header_image_light_url?.trim() ||
      updatedBranding.custom_header_image_dark_url?.trim()
    );
    const hasText = !!(updatedBranding.custom_header_text?.trim());

    if (!hasAnyImage && !hasText) {
      updatedBranding.hide_dukancard_branding = false;
    }

    const { error: updateError } = await supabase
      .from("business_profiles")
      .update({
        custom_branding: updatedBranding
      })
      .eq("id", user.id);

    if (updateError) {
      console.error("Database update error:", updateError);
      return {
        success: false,
        error: "Failed to update branding data in database",
      };
    }

    return {
      success: true,
    };

  } catch (error) {
    console.error("Theme-specific header deletion error:", error);
    return {
      success: false,
      error: "An unexpected error occurred during deletion",
    };
  }
}
