"use client";

import { useRef } from "react";
import { Upload, X, Loader2, Sun, Moon } from "lucide-react";
import NextImage from "next/image";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface ThemeSpecificHeaderUploadProps {
  theme: 'light' | 'dark';
  imageUrl?: string;
  isUploading: boolean;
  isDeleting: boolean;
  isDragging: boolean;
  onFileSelect: (_file: File | null, _theme: 'light' | 'dark') => void;
  onDelete: (_theme: 'light' | 'dark') => void;
  onDrop: (_e: React.DragEvent, _theme: 'light' | 'dark') => void;
  onDragOver: (_e: React.DragEvent, _theme: 'light' | 'dark') => void;
  onDragLeave: (_e: React.DragEvent, _theme: 'light' | 'dark') => void;
}

export default function ThemeSpecificHeaderUpload({
  theme,
  imageUrl,
  isUploading,
  isDeleting,
  isDragging,
  onFileSelect,
  onDelete,
  onDrop,
  onDragOver,
  onDragLeave,
}: ThemeSpecificHeaderUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const ThemeIcon = theme === 'light' ? Sun : Moon;
  const themeLabel = theme === 'light' ? 'Light' : 'Dark';
  const themeColor = theme === 'light' ? 'text-yellow-600' : 'text-blue-600';
  const themeBg = theme === 'light' ? 'bg-yellow-50 dark:bg-yellow-950/20' : 'bg-blue-50 dark:bg-blue-950/20';
  const themeBorder = theme === 'light' ? 'border-yellow-200 dark:border-yellow-800' : 'border-blue-200 dark:border-blue-800';

  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <ThemeIcon className={`h-4 w-4 ${themeColor}`} />
        <span className="text-sm font-medium">{themeLabel} Theme</span>
      </div>

      <Card
        className={`border-dashed border-2 transition-colors cursor-pointer ${
          isDragging
            ? "border-primary bg-primary/5"
            : "border-muted-foreground/25 hover:border-primary/50"
        }`}
        onDrop={(e) => onDrop(e, theme)}
        onDragOver={(e) => onDragOver(e, theme)}
        onDragLeave={(e) => onDragLeave(e, theme)}
        onClick={() => !isUploading && !isDeleting && fileInputRef.current?.click()}
      >
        <CardContent className="p-4">
          {isUploading ? (
            <div className="text-center">
              <div className="w-10 h-10 rounded-lg bg-muted flex items-center justify-center mx-auto mb-2">
                <Loader2 className="h-5 w-5 text-muted-foreground animate-spin" />
              </div>
              <p className="text-xs font-medium mb-1">Uploading...</p>
              <p className="text-xs text-muted-foreground">
                Processing {themeLabel.toLowerCase()} theme image
              </p>
            </div>
          ) : imageUrl ? (
            <div className="space-y-3">
              <div className="relative">
                <div className={`w-full h-16 overflow-hidden rounded-lg border-2 ${themeBorder} ${themeBg}`}>
                  <NextImage
                    src={imageUrl}
                    alt={`${themeLabel} theme header`}
                    width={200}
                    height={64}
                    className="w-full h-full object-contain"
                  />
                </div>
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete(theme);
                  }}
                  disabled={isDeleting}
                  className="absolute top-1 right-1 h-6 w-6 p-0"
                >
                  {isDeleting ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : (
                    <X className="h-3 w-3" />
                  )}
                </Button>
              </div>
              <div className="text-center">
                <p className={`text-xs font-medium ${themeColor}`}>
                  {themeLabel} theme image uploaded
                </p>
                <p className="text-xs text-muted-foreground">
                  Click to replace
                </p>
              </div>
            </div>
          ) : (
            <div className="text-center">
              <div className="w-10 h-10 rounded-lg bg-muted flex items-center justify-center mx-auto mb-2">
                <Upload className="h-5 w-5 text-muted-foreground" />
              </div>
              <p className="text-xs font-medium mb-1">Upload {themeLabel} Image</p>
              <p className="text-xs text-muted-foreground mb-1">
                Drag & drop or click
              </p>
              <p className="text-xs text-muted-foreground">
                PNG • Max 5MB
              </p>
            </div>
          )}
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={(e) => onFileSelect(e.target.files?.[0] || null, theme)}
            className="hidden"
            disabled={isUploading || isDeleting}
          />
        </CardContent>
      </Card>
    </div>
  );
}
