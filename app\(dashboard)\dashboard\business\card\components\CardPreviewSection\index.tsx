"use client";

import { RefObject } from "react";
import { motion } from "framer-motion";
import { BusinessCardData } from "../../schema";
import type { LogoUploadStatus } from "../hooks/useLogoUpload";
import BusinessCardPreview from "../BusinessCardPreview";
import EnhancedCardActions from "@/app/components/shared/EnhancedCardActions";
import { formatAddress } from "@/lib/utils";

interface CardPreviewSectionProps {
  cardData: BusinessCardData;
  logoUploadStatus: LogoUploadStatus;
  localPreviewUrl: string | null;
  userPlan: "basic" | "growth" | "pro" | "enterprise" | "trial" | undefined;
  cardPreviewRef: RefObject<HTMLDivElement | null>; // Changed type here
}

const previewVariants = {
  hidden: { opacity: 0, scale: 0.95 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: { duration: 0.5, delay: 0.1, ease: "easeOut" },
  },
};

export default function CardPreviewSection({
  cardData,
  logoUploadStatus,
  localPreviewUrl,
  userPlan,
  cardPreviewRef,
}: CardPreviewSectionProps) {
  return (
    <motion.div
      variants={previewVariants}
      initial="hidden"
      animate="visible"
      style={{ width: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center' } as React.CSSProperties}
      ref={cardPreviewRef} // Pass the ref here
    >
      <BusinessCardPreview
        data={cardData}
        logoUploadStatus={logoUploadStatus}
        localPreviewUrl={localPreviewUrl}
        userPlan={userPlan}
        isAuthenticated={true}
        totalLikes={cardData.total_likes ?? 0}
        totalSubscriptions={cardData.total_subscriptions ?? 0}
        averageRating={cardData.average_rating ?? 0}
      />

      {/* Share/Download Buttons */}
      <EnhancedCardActions
        businessSlug={cardData.business_slug || ""}
        businessName={cardData.business_name || ""}
        ownerName={cardData.member_name || ""}
        businessAddress={formatAddress(cardData)}
        themeColor={cardData.theme_color || "#F59E0B"}
        className="mt-6"
      />
    </motion.div>
  );
}
