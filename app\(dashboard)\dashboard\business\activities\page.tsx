import { createClient } from '@/utils/supabase/server';
import { redirect } from 'next/navigation';
import { Metadata } from 'next';
import { getBusinessActivities, getUnreadActivitiesCount } from '@/lib/actions/activities';
import ActivitiesPageClient from './components/ActivitiesPageClient';

export const metadata: Metadata = {
  title: "Business Activities - Dukancard",
  robots: "noindex, nofollow",
};

export default async function BusinessActivitiesPage() {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    redirect('/login?message=Please log in to view your business activities.');
  }

  // Fetch initial activities
  const { activities, count, error } = await getBusinessActivities({
    businessProfileId: user.id,
    page: 1,
    pageSize: 15,
    sortBy: "newest",
    filterBy: "all",
  });

  // Get unread count
  const { count: unreadCount } = await getUnreadActivitiesCount(user.id);

  if (error) {
    console.error("Error fetching activities:", error);
  }

  return (
    <ActivitiesPageClient
      initialActivities={activities || []}
      totalCount={count || 0}
      unreadCount={unreadCount || 0}
      businessProfileId={user.id}
    />
  );
}
