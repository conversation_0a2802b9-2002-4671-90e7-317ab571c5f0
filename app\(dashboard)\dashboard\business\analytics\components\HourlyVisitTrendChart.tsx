"use client";

import { motion } from "framer-motion";
import { Clock } from "lucide-react";
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from "recharts";
import { useIsMobile } from "@/hooks/use-mobile";
import { formatIndianNumberShort } from "@/lib/utils";
import PremiumFeatureLock from "./PremiumFeatureLock";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";

interface HourlyVisitTrendChartProps {
  data: { hour: number; visits: number }[];
  userPlan?: string | null;
}

const chartConfig = {
  visits: {
    label: "Visits",
    color: "var(--chart-2)",
  },
} satisfies ChartConfig;

export default function HourlyVisitTrendChart({
  data,
  userPlan,
}: HourlyVisitTrendChartProps) {
  const isMobile = useIsMobile();

  // Format hour number to a readable time format (e.g., 14 -> "2 PM")
  const formatHour = (hour: number) => {
    if (hour === 0) return "12 AM";
    if (hour === 12) return "12 PM";
    return hour < 12 ? `${hour} AM` : `${hour - 12} PM`;
  };

  // Format hour for X-axis ticks - shorter on mobile
  const formatXAxisTick = (hourStr: string) => {
    if (isMobile) {
      // Extract just the hour number for mobile
      const match = hourStr.match(/(\d+)/);
      if (match) {
        return match[1];
      }
      return hourStr;
    }
    return hourStr; // Full formatted hour on desktop
  };

  // Generate all 24 hours
  const generateAllHours = () => {
    const hours = [];
    for (let i = 0; i < 24; i++) {
      hours.push(i);
    }
    return hours;
  };

  // Get all hours
  const allHours = generateAllHours();

  // Create a map of existing data
  const dataMap = new Map();
  data.forEach(item => {
    dataMap.set(item.hour, item.visits);
  });

  // Format data for the chart with all hours, using 0 for missing data
  const chartData = allHours.map(hour => ({
    hour,
    visits: dataMap.has(hour) ? dataMap.get(hour) : 0,
    formattedHour: formatHour(hour),
  }));

  // Calculate the maximum value for better Y-axis scaling
  const maxVisits = Math.max(...chartData.map(item => item.visits));
  // Calculate a nice rounded upper bound for the Y-axis
  const calculateYAxisMax = (maxValue: number) => {
    if (maxValue <= 0) return 5; // Default if no data
    if (maxValue <= 5) return Math.ceil(maxValue * 1.2); // Add 20% padding for small values
    if (maxValue <= 10) return Math.ceil(maxValue * 1.1); // Add 10% padding for medium values
    return Math.ceil(maxValue * 1.05); // Add 5% padding for large values
  };
  const yAxisMax = calculateYAxisMax(maxVisits);





  // Custom formatter for Y-axis ticks using Indian number format
  const formatYAxisTick = (value: number) => {
    return formatIndianNumberShort(Math.floor(value));
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  // Check if user has access to this premium feature
  const isPremiumUser = userPlan === "growth" || userPlan === "pro" || userPlan === "enterprise";

  // If user doesn't have a premium plan, show the premium feature lock component
  if (!isPremiumUser) {
    return (
      <PremiumFeatureLock
        title="Hourly Visit Trend"
        description="Upgrade to Growth plan or higher to see detailed hourly visit trends for your business."
      />
    );
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="rounded-lg border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black p-3 sm:p-4 md:p-6 shadow-sm"
    >
      <div className="mb-4">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
          <h3 className="text-base sm:text-lg font-semibold text-neutral-900 dark:text-neutral-100">
            Hourly Visit Trend
          </h3>
          <div className="flex items-center space-x-2 text-xs sm:text-sm text-neutral-500 dark:text-neutral-400">
            <Clock className="h-3 w-3 sm:h-4 sm:w-4" />
            <span>Today&apos;s visitors by hour</span>
          </div>
        </div>
      </div>

      {/* Chart */}
      <div className="h-[250px] sm:h-[300px] w-full px-1 pb-2">
        <ChartContainer config={chartConfig} className="h-full w-full">
          <AreaChart
            data={chartData}
            margin={isMobile
              ? { top: 5, right: 5, left: 0, bottom: 5 }
              : { top: 10, right: 10, left: 0, bottom: 5 }
            }
          >
            <defs>
              <linearGradient id="fillVisits" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-visits)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-visits)"
                  stopOpacity={0.1}
                />
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" vertical={false} />
            <XAxis
              dataKey="formattedHour"
              axisLine={false}
              tickLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={formatXAxisTick}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              domain={[0, yAxisMax]}
              allowDecimals={false}
              tickFormatter={formatYAxisTick}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  labelFormatter={(label) => `Time: ${label}`}
                  formatter={(value) => [
                    formatIndianNumberShort(Number(value)),
                    " Visits"
                  ]}
                />
              }
            />
            <Area
              dataKey="visits"
              type="natural"
              fill="url(#fillVisits)"
              stroke="var(--color-visits)"
              strokeWidth={isMobile ? 1.5 : 2}
            />
          </AreaChart>
        </ChartContainer>
      </div>
    </motion.div>
  );
}
