/**
 * PAYMENT METHOD DETECTOR
 * 
 * Enhanced payment method detection for accurate subscription flow decisions.
 * This module provides reliable payment method detection by querying Razorpay API
 * when needed, with fallback to stored database values.
 */

import { SubscriptionFlowManager } from './SubscriptionFlowManager';

export interface PaymentMethodDetectionResult {
  method: 'CARD' | 'UPI_EMANDATE' | 'UNKNOWN';
  source: 'RAZORPAY_API' | 'DATABASE_FALLBACK' | 'UNKNOWN';
  confidence: 'HIGH' | 'MEDIUM' | 'LOW';
  details?: {
    razorpayMethod?: string;
    lastPaymentId?: string;
    subscriptionNotes?: Record<string, unknown>;
  };
}

export class PaymentMethodDetector {
  /**
   * Detect payment method with high accuracy for plan switching decisions
   */
  static async detectPaymentMethod(
    razorpaySubscriptionId: string | null,
    fallbackMethod?: string | null
  ): Promise<PaymentMethodDetectionResult> {
    // If no Razorpay subscription ID, use fallback
    if (!razorpaySubscriptionId) {
      return {
        method: SubscriptionFlowManager['categorizePaymentMethod'](fallbackMethod),
        source: 'DATABASE_FALLBACK',
        confidence: 'LOW',
        details: {}
      };
    }

    try {
      // Import Razorpay services
      const { getSubscription } = await import('@/lib/razorpay/services/subscription');
      const { getPayment } = await import('@/lib/razorpay/services/payment');
      
      // Fetch subscription details from Razorpay
      const subscriptionResult = await getSubscription(razorpaySubscriptionId);
      
      if (!subscriptionResult.success || !subscriptionResult.data) {
        console.warn('[PAYMENT_DETECTOR] Failed to fetch subscription from Razorpay:', subscriptionResult.error);
        return {
          method: SubscriptionFlowManager['categorizePaymentMethod'](fallbackMethod),
          source: 'DATABASE_FALLBACK',
          confidence: 'LOW',
          details: {}
        };
      }

      const subscription = subscriptionResult.data;
      const notes = subscription.notes || {};

      // Method 1: Check if payment method is explicitly stored in subscription notes
      if (notes.payment_method && typeof notes.payment_method === 'string') {
        const method = SubscriptionFlowManager['categorizePaymentMethod'](notes.payment_method);
        return {
          method,
          source: 'RAZORPAY_API',
          confidence: 'HIGH',
          details: {
            razorpayMethod: notes.payment_method,
            subscriptionNotes: notes
          }
        };
      }

      // Method 2: Fetch payment method from the most recent payment
      if (notes.last_payment_id && typeof notes.last_payment_id === 'string') {
        const paymentResult = await getPayment(notes.last_payment_id);
        
        if (paymentResult.success && paymentResult.data) {
          const method = SubscriptionFlowManager['categorizePaymentMethod'](paymentResult.data.method);
          return {
            method,
            source: 'RAZORPAY_API',
            confidence: 'HIGH',
            details: {
              razorpayMethod: paymentResult.data.method,
              lastPaymentId: notes.last_payment_id,
              subscriptionNotes: notes
            }
          };
        } else {
          console.warn('[PAYMENT_DETECTOR] Failed to fetch payment details:', paymentResult.error);
        }
      }

      // Method 3: Try to infer from subscription status and other indicators
      // This is less reliable but better than complete fallback
      if (subscription.status === 'active' && subscription.auth_attempts === 0) {
        // Likely a card payment if no auth attempts were needed
        return {
          method: 'CARD',
          source: 'RAZORPAY_API',
          confidence: 'MEDIUM',
          details: {
            subscriptionNotes: notes
          }
        };
      }

      // Fallback to database value
      console.warn('[PAYMENT_DETECTOR] Could not determine payment method from Razorpay, using fallback');
      return {
        method: SubscriptionFlowManager['categorizePaymentMethod'](fallbackMethod),
        source: 'DATABASE_FALLBACK',
        confidence: 'LOW',
        details: {
          subscriptionNotes: notes
        }
      };

    } catch (error) {
      console.error('[PAYMENT_DETECTOR] Error detecting payment method:', error);
      return {
        method: SubscriptionFlowManager['categorizePaymentMethod'](fallbackMethod),
        source: 'DATABASE_FALLBACK',
        confidence: 'LOW',
        details: {}
      };
    }
  }

  /**
   * Quick payment method check for UI display
   */
  static async getPaymentMethodForDisplay(
    razorpaySubscriptionId: string | null,
    fallbackMethod?: string | null
  ): Promise<string> {
    const result = await this.detectPaymentMethod(razorpaySubscriptionId, fallbackMethod);
    
    switch (result.method) {
      case 'CARD':
        return 'Card';
      case 'UPI_EMANDATE':
        return result.details?.razorpayMethod === 'upi' ? 'UPI' : 'E-Mandate';
      default:
        return 'Unknown';
    }
  }

  /**
   * Check if plan switching requires user confirmation (UPI/E-Mandate warning)
   */
  static async requiresPlanSwitchWarning(
    razorpaySubscriptionId: string | null,
    fallbackMethod?: string | null
  ): Promise<{
    requiresWarning: boolean;
    paymentMethod: string;
    confidence: 'HIGH' | 'MEDIUM' | 'LOW';
  }> {
    const result = await this.detectPaymentMethod(razorpaySubscriptionId, fallbackMethod);
    
    return {
      requiresWarning: result.method === 'UPI_EMANDATE',
      paymentMethod: await this.getPaymentMethodForDisplay(razorpaySubscriptionId, fallbackMethod),
      confidence: result.confidence
    };
  }
}
