"use client";

import { Spark<PERSON>, Lock, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";

interface PremiumFeatureLockProps {
  title: string;
  description?: string;
  height?: string;
}

export default function PremiumFeatureLock({
  title,
  description = "Upgrade to Basic plan or higher to unlock this premium analytics feature.",
  height = "h-[350px]",
}: PremiumFeatureLockProps) {
  return (
    <div
      className={`w-full ${height} flex items-center justify-center rounded-lg border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-neutral-900 overflow-hidden relative`}
    >
      {/* Background gradient effect */}
      <div className="absolute inset-0 bg-gradient-to-br from-transparent to-amber-50 dark:to-amber-950/20 opacity-50" />

      {/* Lock icon with glow effect */}
      <div className="absolute opacity-10 text-amber-500 dark:text-amber-400">
        <Lock className="w-32 h-32" />
      </div>

      <div className="z-10 text-center max-w-md px-6">
        <div className="flex items-center justify-center gap-2 mb-3">
          <Sparkles className="h-5 w-5 text-amber-500 dark:text-amber-400" />
          <h3 className="text-lg font-medium text-amber-700 dark:text-amber-300">
            Premium Feature: {title}
          </h3>
        </div>

        <p className="text-neutral-600 dark:text-neutral-400 mb-6">
          {description}
        </p>

        <div>
          <Button asChild className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white">
            <Link href="/dashboard/business/plan" className="flex items-center gap-2">
              Upgrade Now
              <ArrowRight className="h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
