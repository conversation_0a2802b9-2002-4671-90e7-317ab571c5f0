"use client";

import { useState, useEffect } from "react";
import { UseFormReturn } from "react-hook-form";
import { Link, MessageSquare, Instagram, Facebook } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { BusinessCardData } from "../../schema";

interface LinksSectionProps {
  form: UseFormReturn<BusinessCardData>;
}

export default function LinksSection({ form }: LinksSectionProps) {
  const primaryPhone = form.watch("phone");

  // Local state for copy checkboxes
  const [usePrimaryForWhatsapp, setUsePrimaryForWhatsapp] = useState(false);

  // Effect to update whatsapp_number when checkbox/primary phone changes
  useEffect(() => {
    if (usePrimaryForWhatsapp) {
      form.setValue("whatsapp_number", primaryPhone || "", {
        shouldValidate: true,
        shouldDirty: true,
      });
    }
  }, [usePrimaryForWhatsapp, primaryPhone, form]);

  return (
    <div className="rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-6 mb-4 md:mb-6 transition-all duration-300 hover:shadow-lg">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800">
        <div className="p-2 rounded-lg bg-primary/10 text-primary self-start">
          <Link className="w-4 sm:w-5 h-4 sm:h-5" />
        </div>
        <div className="flex-1">
          <h3 className="text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100">
            Links
          </h3>
          <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5">
            Add your social media and communication links
          </p>
        </div>
      </div>

      <div className="flex flex-col gap-4 sm:gap-6">
        {/* WhatsApp Number and Instagram URL Fields - 2 columns on tablet and desktop */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
          {/* WhatsApp Number Field */}
          <FormField
            control={form.control}
            name="whatsapp_number"
            render={({ field }) => (
              <FormItem className="space-y-1 sm:space-y-2">
                <FormLabel className="text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5">
                  <MessageSquare className="h-3.5 w-3.5 text-primary" />
                  WhatsApp Number
                </FormLabel>
                <div className="flex flex-col space-y-2">
                  <FormControl>
                    <div className="relative">
                      <MessageSquare className="absolute left-3 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-neutral-500 dark:text-neutral-400" />
                      <Input
                        type="tel"
                        pattern="[0-9]*"
                        inputMode="numeric"
                        placeholder="9876543210"
                        {...field}
                        onChange={(e) => {
                          // Remove any +91 prefix if user enters it
                          let value = e.target.value.replace(/^\+91/, '');
                          // Only allow numeric input and limit to 10 digits
                          value = value.replace(/\D/g, '');
                          if (value.length <= 10) {
                            field.onChange(value);
                          }
                        }}
                        onKeyDown={(e) => {
                          // Prevent non-numeric input
                          const isNumeric = /^[0-9]$/.test(e.key);
                          const isControl = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(e.key);
                          if (!isNumeric && !isControl) {
                            e.preventDefault();
                          }
                        }}
                        className="w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 pl-10 pr-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"
                        disabled={usePrimaryForWhatsapp}
                      />
                    </div>
                  </FormControl>
                  <div className="flex items-center space-x-1">
                    <Checkbox
                      id="copy_whatsapp"
                      checked={usePrimaryForWhatsapp}
                      onCheckedChange={(checked) =>
                        setUsePrimaryForWhatsapp(Boolean(checked))
                      }
                      disabled={!primaryPhone}
                    />
                    <Label
                      htmlFor="copy_whatsapp"
                      className="text-xs font-normal cursor-pointer text-neutral-700 dark:text-neutral-300"
                    >
                      Use primary phone
                    </Label>
                  </div>
                </div>
                <FormDescription className="text-xs text-neutral-500 dark:text-neutral-400 ml-1">
                  Used to generate wa.me link. Enter 10-digit mobile number.
                </FormDescription>
                <FormMessage className="text-xs text-red-500" />
              </FormItem>
            )}
          />

          {/* Instagram URL Field */}
          <FormField
            control={form.control}
            name="instagram_url"
            render={({ field }) => (
              <FormItem className="space-y-1 sm:space-y-2">
                <FormLabel className="text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5">
                  <Instagram className="h-3.5 w-3.5 text-primary" />
                  Instagram Profile URL
                </FormLabel>
                <FormControl>
                  <div className="relative">
                    <Instagram className="absolute left-3 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-neutral-500 dark:text-neutral-400" />
                    <Input
                      type="url"
                      placeholder="https://instagram.com/yourprofile"
                      {...field}
                      className="w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 pl-10 pr-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"
                    />
                  </div>
                </FormControl>
                <FormDescription className="text-xs text-neutral-500 dark:text-neutral-400 ml-1">
                  Your Instagram profile link
                </FormDescription>
                <FormMessage className="text-xs text-red-500" />
              </FormItem>
            )}
          />
        </div>

        {/* Facebook URL Field */}
        <FormField
          control={form.control}
          name="facebook_url"
          render={({ field }) => (
            <FormItem className="space-y-1 sm:space-y-2">
              <FormLabel className="text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5">
                <Facebook className="h-3.5 w-3.5 text-primary" />
                Facebook Page URL
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <Facebook className="absolute left-3 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-neutral-500 dark:text-neutral-400" />
                  <Input
                    type="url"
                    placeholder="https://facebook.com/yourpage"
                    {...field}
                    className="w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 pl-10 pr-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"
                  />
                </div>
              </FormControl>
              <FormDescription className="text-xs text-neutral-500 dark:text-neutral-400 ml-1">
                Your Facebook page link
              </FormDescription>
              <FormMessage className="text-xs text-red-500" />
            </FormItem>
          )}
        />
      </div>

      {/* Tip Section */}
      <div className="mt-4 sm:mt-6 rounded-lg bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-950/30 dark:to-purple-950/20 p-3 sm:p-4 border border-violet-100 dark:border-violet-900/30 shadow-sm">
        <div className="flex items-start gap-2 sm:gap-3">
          <div className="p-1.5 rounded-full bg-violet-100 dark:bg-violet-900/60 text-violet-600 dark:text-violet-300 mt-0.5 shadow-sm">
            <Link className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
          </div>
          <div>
            <p className="text-xs sm:text-sm font-medium text-violet-800 dark:text-violet-300">
              Links Tip
            </p>
            <p className="text-xs text-violet-700 dark:text-violet-400 mt-0.5 sm:mt-1 leading-relaxed">
              Including your WhatsApp number and social media links makes it easier for customers to connect with you instantly.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}