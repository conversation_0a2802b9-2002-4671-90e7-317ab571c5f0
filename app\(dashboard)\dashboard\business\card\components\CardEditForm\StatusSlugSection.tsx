"use client";

import { useState, useEffect, useCallback, useRef, useMemo } from "react";
import { UseFormReturn } from "react-hook-form";
import {
  Globe,
  CheckCircle,
  XCircle,
  Loader2,
  Info,
  Check,
  AlertCircle,
  User,
  Building2,
  Phone,
  MapPin,
  EyeOff,
  Eye,
  Link,
} from "lucide-react";
import { debounce } from "lodash";
import { motion } from "framer-motion";
import { checkSlugAvailability } from "../../actions";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { BusinessCardData, requiredFieldsForOnline } from "../../schema";

interface StatusSlugSectionProps {
  form: UseFormReturn<BusinessCardData>;
  slugStatus: {
    checking: boolean;
    available: boolean | null;
    message: string | null;
  };
  canGoOnline: boolean;
  isSubscriptionHalted?: boolean;
  onSlugCheckingChange?: (_isChecking: boolean) => void;
}

export default function StatusSlugSection({
  form,
  canGoOnline,
  isSubscriptionHalted = false,
  onSlugCheckingChange: _onSlugCheckingChange,
}: StatusSlugSectionProps) {
  // Internal state for slug checking
  const [isCheckingSlug, setIsCheckingSlug] = useState(false);
  const [slugAvailable, setSlugAvailable] = useState<boolean | null>(null);
  const [slugToCheck, setSlugToCheck] = useState<string>("");

  // Helper function to get missing required fields - wrapped in useCallback to prevent recreation on every render
  const getMissingFields = useCallback((): string[] => {
    const formValues = form.getValues();
    return requiredFieldsForOnline.filter(
      (field) => !formValues[field] || String(formValues[field]).trim() === ""
    );
  }, [form]);

  // Debug information - log to console
  useEffect(() => {
    console.log("StatusSlugSection - canGoOnline:", canGoOnline);
    console.log("Missing fields:", getMissingFields());
  }, [canGoOnline, getMissingFields]);

  // Create a debounced slug check function
  const performSlugCheck = useCallback(
    async (slug: string) => {
      // Don't check if slug is empty or too short
      if (!slug || slug.length < 3 || !/^[a-z0-9-]+$/.test(slug)) {
        setSlugAvailable(null); // Reset if slug is invalid format or too short
        return;
      }

      // Skip check if it's the same as the current value and we already know it's available
      const currentValue = form.getValues("business_slug");
      if (currentValue === slug && slugAvailable === true) {
        return;
      }

      setIsCheckingSlug(true);
      _onSlugCheckingChange?.(true);
      try {
        const { available } = await checkSlugAvailability(slug);
        setSlugAvailable(available);
      } catch (error) {
        console.error("Error checking slug availability:", error);
        setSlugAvailable(false);
      } finally {
        setIsCheckingSlug(false);
        _onSlugCheckingChange?.(false);
      }
    },
    [form, slugAvailable, _onSlugCheckingChange]
  );

  const debouncedSlugCheck = useMemo(
    () => debounce(performSlugCheck, 500),
    [performSlugCheck]
  );

  // Clean up debounce on unmount
  useEffect(() => {
    return () => {
      debouncedSlugCheck.cancel();
    };
  }, [debouncedSlugCheck]);

  // Trigger slug check when slugToCheck changes
  useEffect(() => {
    if (slugToCheck) {
      debouncedSlugCheck(slugToCheck);
    }
  }, [slugToCheck, debouncedSlugCheck]);

  // Set form error when slug is not available
  useEffect(() => {
    if (slugAvailable === false) {
      form.setError("business_slug", {
        type: "manual",
        message: "This URL slug is already taken.",
      });
    } else if (slugAvailable === true) {
      form.clearErrors("business_slug");
    }
  }, [slugAvailable, form]);

  // Initialize slug check with current value - use a ref to ensure it only runs once
  const initialCheckDone = useRef(false);

  useEffect(() => {
    if (!initialCheckDone.current) {
      const currentSlug = form.getValues("business_slug");
      if (currentSlug && currentSlug.length >= 3) {
        setSlugToCheck(currentSlug);
      }
      initialCheckDone.current = true;
    }
  }, [form]); // Keep form in dependencies for consistency

  // Use internal slug status instead of external one
  const slugStatus = {
    checking: isCheckingSlug,
    available: slugAvailable,
    message:
      slugAvailable === true
        ? "Slug is available!"
        : slugAvailable === false
        ? "Slug is already taken."
        : null,
  };
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
      className="rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-lg p-4 sm:p-5 md:p-6 mb-4 md:mb-6 transition-all duration-300 hover:shadow-xl"
    >
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4 mb-5 sm:mb-6 pb-4 border-b border-neutral-100 dark:border-neutral-800">
        <div className="p-3 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 text-primary self-start shadow-sm">
          <Globe className="w-5 sm:w-6 h-5 sm:h-6" />
        </div>
        <div className="flex-1">
          <h3 className="text-base sm:text-xl font-semibold text-neutral-800 dark:text-neutral-100">
            Card Status & URL
          </h3>
          <p className="text-sm text-neutral-500 dark:text-neutral-400 mt-1">
            Configure your card&apos;s visibility and unique URL
          </p>
        </div>
      </div>

      <div className="flex flex-col gap-4 sm:gap-6">
        {/* Card Status Field */}
        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem className="space-y-3 sm:space-y-4">
              <FormLabel className="text-sm font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-2">
                <Globe className="h-4 w-4 text-primary" />
                Card Status
              </FormLabel>
              <RadioGroup
                onValueChange={field.onChange}
                value={field.value}
                className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-5"
              >
                {/* Offline Option */}
                <Label htmlFor="status-offline" className="cursor-pointer">
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    transition={{ type: "spring", stiffness: 400, damping: 17 }}
                    className={`relative h-full rounded-xl overflow-hidden border-2 transition-all duration-300 shadow-sm
                      ${field.value === "offline"
                        ? "border-[--theme-color] bg-[--theme-color]/5 shadow-[--theme-color]/10"
                        : "border-neutral-200 dark:border-neutral-700 hover:border-neutral-300 dark:hover:border-neutral-600"
                      }
                    `}
                  >
                    {field.value === "offline" && (
                      <div className="absolute top-0 left-0 w-full h-1 bg-[--theme-color]"></div>
                    )}
                    <div className="p-4 sm:p-5">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-3">
                          <div className={`p-2 rounded-full ${field.value === "offline" ? "bg-[--theme-color]/20" : "bg-neutral-100 dark:bg-neutral-800"}`}>
                            <EyeOff className={`h-5 w-5 ${field.value === "offline" ? "text-[--theme-color]" : "text-neutral-500 dark:text-neutral-400"}`} />
                          </div>
                          <h4 className="text-base font-semibold text-neutral-800 dark:text-neutral-100">
                            Offline (Private)
                          </h4>
                        </div>
                        <RadioGroupItem
                          value="offline"
                          id="status-offline"
                          className="translate-y-0"
                        />
                      </div>
                      <p className="text-sm text-neutral-600 dark:text-neutral-300 leading-relaxed">
                        Your card is not publicly visible. Use for expired plans
                        or private cards.
                      </p>
                    </div>
                  </motion.div>
                </Label>

                {/* Online Option */}
                <Label
                  htmlFor="status-online"
                  className={`${!canGoOnline ? "cursor-not-allowed" : "cursor-pointer"}`}
                >
                  <motion.div
                    whileHover={canGoOnline ? { scale: 1.02 } : {}}
                    whileTap={canGoOnline ? { scale: 0.98 } : {}}
                    transition={{ type: "spring", stiffness: 400, damping: 17 }}
                    className={`relative h-full rounded-xl overflow-hidden border-2 transition-all duration-300 shadow-sm
                      ${field.value === "online"
                        ? "border-[--theme-color] bg-[--theme-color]/5 shadow-[--theme-color]/10"
                        : "border-neutral-200 dark:border-neutral-700"
                      }
                      ${!canGoOnline ? "opacity-70" : "hover:border-neutral-300 dark:hover:border-neutral-600"}
                    `}
                  >
                    {field.value === "online" && (
                      <div className="absolute top-0 left-0 w-full h-1 bg-[--theme-color]"></div>
                    )}
                    <div className="p-4 sm:p-5">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-3">
                          <div className={`p-2 rounded-full ${field.value === "online" ? "bg-[--theme-color]/20" : "bg-neutral-100 dark:bg-neutral-800"}`}>
                            <Eye className={`h-5 w-5 ${field.value === "online" ? "text-[--theme-color]" : "text-neutral-500 dark:text-neutral-400"}`} />
                          </div>
                          <h4 className="text-base font-semibold text-neutral-800 dark:text-neutral-100">
                            Online (Public)
                          </h4>
                        </div>
                        <RadioGroupItem
                          value="online"
                          id="status-online"
                          disabled={!canGoOnline}
                          className="translate-y-0"
                        />
                      </div>
                      <p className="text-sm text-neutral-600 dark:text-neutral-300 leading-relaxed">
                        Make your card accessible via a unique URL. Requires an
                        active plan and unique slug.
                      </p>

                      {isSubscriptionHalted && (
                        <div className="mt-3 p-3 rounded-lg bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800/50">
                          <p className="text-xs text-red-700 dark:text-red-400 flex items-center font-medium mb-2">
                            <AlertCircle className="w-3.5 h-3.5 mr-1.5 flex-shrink-0" />
                            Subscription Paused
                          </p>
                          <p className="text-xs text-red-600 dark:text-red-400">
                            Your subscription is currently paused. You cannot set your card to online status until you resume your subscription.
                            Please visit the Plan page to resume your subscription.
                          </p>
                        </div>
                      )}

                      {!canGoOnline && !isSubscriptionHalted && (
                        <div className="mt-3 p-3 rounded-lg bg-amber-50 dark:bg-amber-950/30 border border-amber-200 dark:border-amber-800/50">
                          <p className="text-xs text-amber-700 dark:text-amber-400 flex items-center font-medium mb-2">
                            <AlertCircle className="w-3.5 h-3.5 mr-1.5 flex-shrink-0" />
                            Required fields to go online:
                          </p>
                          <div className="grid grid-cols-2 gap-x-3 gap-y-1.5">
                            {/* Map through required fields and show their status */}
                            {(() => {
                              const missingFields = getMissingFields();
                              const fieldIcons = {
                                member_name: <User className="w-3 h-3 mr-1.5 flex-shrink-0" />,
                                title: <User className="w-3 h-3 mr-1.5 flex-shrink-0" />,
                                business_name: <Building2 className="w-3 h-3 mr-1.5 flex-shrink-0" />,
                                phone: <Phone className="w-3 h-3 mr-1.5 flex-shrink-0" />,
                                address_line: <Building2 className="w-3 h-3 mr-1.5 flex-shrink-0" />,
                                pincode: <MapPin className="w-3 h-3 mr-1.5 flex-shrink-0" />,
                                city: <MapPin className="w-3 h-3 mr-1.5 flex-shrink-0" />,
                                state: <MapPin className="w-3 h-3 mr-1.5 flex-shrink-0" />,
                                locality: <MapPin className="w-3 h-3 mr-1.5 flex-shrink-0" />,
                              };

                              const fieldLabels = {
                                member_name: "Your Name",
                                title: "Your Title",
                                business_name: "Business Name",
                                phone: "Primary Phone",
                                address_line: "Address Line",
                                pincode: "Pincode",
                                city: "City",
                                state: "State",
                                locality: "Locality",
                              };

                              return requiredFieldsForOnline.map((field) => {
                                const isMissing = missingFields.includes(field);
                                return (
                                  <p
                                    key={field}
                                    className={`text-xs flex items-center ${
                                      isMissing
                                        ? "text-red-500 dark:text-red-400 font-medium"
                                        : "text-green-500 dark:text-green-400"
                                    }`}
                                  >
                                    {fieldIcons[field as keyof typeof fieldIcons]}
                                    {fieldLabels[field as keyof typeof fieldLabels]}
                                    {!isMissing && <Check className="w-3 h-3 ml-1 flex-shrink-0" />}
                                  </p>
                                );
                              });
                            })()}
                          </div>
                        </div>
                      )}
                    </div>
                  </motion.div>
                </Label>
              </RadioGroup>
              <FormMessage className="text-xs text-red-500" />
            </FormItem>
          )}
        />

        {/* Unique Card URL (Slug) Field */}
        {form.watch("status") === "online" && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <FormField
              control={form.control}
              name="business_slug"
              render={({ field }) => (
                <FormItem className="space-y-3 sm:space-y-4 mt-2">
                  <FormLabel className="text-sm font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-2">
                    <Link className="h-4 w-4 text-primary" />
                    Unique Card URL
                  </FormLabel>

                  <div className="p-4 sm:p-5 rounded-xl border border-neutral-200 dark:border-neutral-700 bg-neutral-50/50 dark:bg-neutral-800/30 shadow-sm">
                    <div className="flex flex-col space-y-3">
                      {/* Input Field */}
                      <div className="flex items-center space-x-0 rounded-lg overflow-hidden border-2 border-neutral-200 dark:border-neutral-700 focus-within:border-primary/70 focus-within:ring-2 focus-within:ring-primary/20 transition-all duration-200 shadow-sm">
                        <div className="bg-neutral-100 dark:bg-neutral-800 px-3 sm:px-4 py-3 border-r border-neutral-200 dark:border-neutral-700 text-neutral-600 dark:text-neutral-300 text-sm font-medium flex items-center">
                          <Globe className="h-4 w-4 mr-2 text-neutral-500 dark:text-neutral-400" />
                          dukancard.in/
                        </div>
                        <div className="relative flex-1">
                          <FormControl>
                            <Input
                              placeholder="your-unique-name"
                              {...field}
                              className="w-full border-0 bg-white dark:bg-black py-3 px-4 text-base focus:ring-0 shadow-none"
                              onChange={(e) => {
                                // Format the slug to ensure it's valid
                                const slug = e.target.value
                                  .toLowerCase()
                                  .replace(/\s+/g, "-")
                                  .replace(/[^a-z0-9-]/g, "");

                                // Update the form field with the formatted slug
                                field.onChange(slug);

                                // Trigger slug check
                                setSlugToCheck(slug);
                                setSlugAvailable(null);
                              }}
                            />
                          </FormControl>
                          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                            {slugStatus.checking && (
                              <div className="bg-neutral-100 dark:bg-neutral-800 p-1 rounded-full">
                                <Loader2 className="w-5 h-5 text-primary animate-spin" />
                              </div>
                            )}
                            {!slugStatus.checking && slugStatus.available === true && (
                              <div className="bg-green-50 dark:bg-green-900/30 p-1 rounded-full">
                                <CheckCircle className="w-5 h-5 text-green-500 dark:text-green-400" />
                              </div>
                            )}
                            {!slugStatus.checking && slugStatus.available === false && (
                              <div className="bg-red-50 dark:bg-red-900/30 p-1 rounded-full">
                                <XCircle className="w-5 h-5 text-red-500 dark:text-red-400" />
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* URL Preview */}
                      <div className="flex items-center">
                        <div className="px-3 py-2 rounded-lg bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300 text-sm font-mono flex items-center">
                          <Link className="h-4 w-4 mr-2 text-neutral-500 dark:text-neutral-400" />
                          https://dukancard.in/{field.value || "your-unique-name"}
                        </div>
                      </div>

                      {/* Status Message */}
                      <div className="flex items-center h-6 px-1">
                        {slugStatus.checking && (
                          <p className="text-sm text-neutral-500 flex items-center">
                            <Loader2 className="w-3.5 h-3.5 mr-2 animate-spin" />
                            Checking availability...
                          </p>
                        )}
                        {!slugStatus.checking && slugStatus.available === true && !form.formState.errors.business_slug && (
                          <p className="text-sm text-green-600 dark:text-green-400 flex items-center">
                            <CheckCircle className="w-3.5 h-3.5 mr-2" />
                            URL is available!
                          </p>
                        )}
                        {!slugStatus.checking && slugStatus.available === false && (
                          <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
                            <AlertCircle className="w-3.5 h-3.5 mr-2" />
                            URL is already taken.
                          </p>
                        )}
                        {!slugStatus.checking && slugStatus.available === null && (
                          <p className="text-sm text-neutral-600 dark:text-neutral-400 flex items-center">
                            <Info className="w-3.5 h-3.5 mr-2" />
                            Lowercase letters, numbers, hyphens only. Min 3 chars.
                          </p>
                        )}
                      </div>
                    </div>
                  </div>

                  <FormMessage className="text-sm text-red-500" />
                </FormItem>
              )}
            />
          </motion.div>
        )}
      </div>

      {/* Tip Section */}
      <div className="mt-4 sm:mt-6 rounded-lg bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-950/30 dark:to-purple-950/20 p-3 sm:p-4 border border-violet-100 dark:border-violet-900/30 shadow-sm">
        <div className="flex items-start gap-2 sm:gap-3">
          <div className="p-1.5 rounded-full bg-violet-100 dark:bg-violet-900/60 text-violet-600 dark:text-violet-300 mt-0.5 shadow-sm">
            <Info className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
          </div>
          <div>
            <p className="text-xs sm:text-sm font-medium text-violet-800 dark:text-violet-300">
              Status & URL Tip
            </p>
            <p className="text-xs text-violet-700 dark:text-violet-400 mt-0.5 sm:mt-1 leading-relaxed">
              Choose a unique, memorable slug for your card&apos;s URL to make
              it easy to share. Set to &apos;Online&apos; to make your card
              publicly accessible.
            </p>
            <p className="text-xs text-violet-700 dark:text-violet-400 mt-1.5 leading-relaxed">
              To set your card status to &apos;Online&apos;, you must fill in all required fields:
              your name, title, business name, primary phone, address line, pincode, city, state, and locality.
            </p>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
