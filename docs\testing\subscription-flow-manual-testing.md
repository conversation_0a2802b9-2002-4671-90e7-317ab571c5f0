# Subscription Flow Manual Testing Guide

This guide provides step-by-step instructions for manually testing all subscription flow scenarios in the business dashboard plan page.

## Prerequisites

1. Access to the business dashboard at `/dashboard/business/plan`
2. Test business account with appropriate permissions
3. Access to Supabase admin panel for database verification
4. Access to Razorpay dashboard for payment verification

## Test Scenarios

### Scenario 1: Trial User Authorizes Payment for Post-Trial

**Setup:**
- User is in active trial period
- Trial end date is in the future
- No existing Razorpay subscription

**Steps:**
1. Navigate to `/dashboard/business/plan`
2. Verify trial status is displayed
3. Click on a paid plan (Basic, Growth, Pro, or Enterprise)
4. Click "Subscribe Now" in the plan dialog
5. Verify Razorpay checkout opens for authorization (not immediate payment)
6. Complete the authorization process
7. Verify subscription status changes to "authenticated"
8. Wait for trial end date to pass
9. Verify automatic transition to active subscription

**Expected Results:**
- Subscription created with `authenticated` status
- Database updated only after webhook confirmation
- Trial period continues until end date
- Automatic activation after trial ends

**Database Verification:**
```sql
-- Check subscription status
SELECT subscription_status, plan_id, subscription_start_date 
FROM payment_subscriptions 
WHERE business_profile_id = 'USER_ID';

-- Check business profile
SELECT has_active_subscription, trial_end_date 
FROM business_profiles 
WHERE id = 'USER_ID';
```

### Scenario 2: Trial User Doesn't Subscribe (Auto-Downgrade)

**Setup:**
- User is in trial period
- Trial end date passes without subscription

**Steps:**
1. Set trial end date to past date in database
2. Navigate to `/dashboard/business/plan`
3. Verify user is shown as inactive/free plan
4. Check product limits are enforced (5 products max)

**Expected Results:**
- User automatically downgraded to free plan
- Product limits enforced (5 products available)
- No active subscription

**Database Verification:**
```sql
-- Check product availability
SELECT COUNT(*) as total_products, 
       COUNT(*) FILTER (WHERE is_available = true) as available_products
FROM products_services 
WHERE business_id = 'USER_ID';
```

### Scenario 3: Trial User Switches Plans

**Setup:**
- User is in trial with authorized subscription
- User wants to switch to different plan

**Steps:**
1. Have an authenticated subscription for Plan A
2. Navigate to plan page
3. Select Plan B and click "Subscribe Now"
4. Complete payment authorization
5. Verify new subscription is created
6. Verify old subscription is cancelled after new one is authenticated

**Expected Results:**
- New subscription created for Plan B
- Old subscription cancelled after new one is active
- Seamless transition without service interruption

### Scenario 4: Trial User Cancels Authorized Subscription

**Setup:**
- User has authorized subscription but trial is still active

**Steps:**
1. Navigate to subscription management tab
2. Click "Cancel Subscription"
3. Confirm cancellation
4. Verify subscription is cancelled
5. Verify user reverts to trial status

**Expected Results:**
- Subscription cancelled in Razorpay
- User reverts to trial status
- Plan selection preserved for future subscription

### Scenario 5: Post-Trial User Subscribes (Upfront Payment)

**Setup:**
- User's trial has ended
- No active subscription
- User is on free plan

**Steps:**
1. Navigate to plan page
2. Select a paid plan
3. Click "Subscribe Now"
4. Verify immediate payment is required (not just authorization)
5. Complete payment
6. Verify immediate activation

**Expected Results:**
- Immediate payment required
- Subscription activated immediately after payment
- Database updated after webhook confirmation

### Scenario 6: Active User Switches Plans (Payment Method Check)

**Setup:**
- User has active paid subscription
- Test both card and UPI/E-Mandate payment methods

**Card Payment Test:**
1. Have active subscription paid via card
2. Navigate to plan page
3. Select different plan
4. Verify direct plan update (no warning dialog)
5. Complete the update process

**UPI/E-Mandate Test:**
1. Have active subscription paid via UPI/E-Mandate
2. Navigate to plan page
3. Select different plan
4. Verify warning dialog appears about UPI/E-Mandate limitations
5. Confirm to proceed
6. Verify new subscription creation and old subscription cancellation

**Expected Results:**
- Card payments: Direct subscription update
- UPI/E-Mandate: Warning dialog + new subscription creation

### Scenario 7: Active User Cancels Subscription

**Setup:**
- User has active paid subscription

**Steps:**
1. Navigate to subscription management tab
2. Click "Cancel Subscription"
3. Confirm cancellation
4. Verify downgrade to free plan
5. Verify product limits are enforced

**Expected Results:**
- Subscription cancelled
- User downgraded to free plan
- Product limits enforced immediately

### Scenario 8: Active User Pauses/Resumes Subscription

**Pause Test:**
1. Navigate to subscription management tab
2. Click "Pause Subscription"
3. Confirm pause
4. Verify subscription is paused
5. Verify downgrade to free plan
6. Verify original plan details are stored

**Resume Test:**
1. Click "Resume Subscription"
2. Confirm resume
3. Verify subscription is reactivated
4. Verify original plan is restored
5. Verify product limits are restored

**Expected Results:**
- Pause: Downgrade to free, original plan stored
- Resume: Original plan restored, full access restored

## Database Verification Queries

### Check Subscription Status
```sql
SELECT 
  ps.razorpay_subscription_id,
  ps.subscription_status,
  ps.plan_id,
  ps.plan_cycle,
  ps.original_plan_id,
  ps.subscription_paused_at,
  bp.has_active_subscription
FROM payment_subscriptions ps
JOIN business_profiles bp ON ps.business_profile_id = bp.id
WHERE ps.business_profile_id = 'USER_ID'
ORDER BY ps.created_at DESC
LIMIT 1;
```

### Check Product Limits
```sql
SELECT 
  plan_id,
  COUNT(*) as total_products,
  COUNT(*) FILTER (WHERE is_available = true) as available_products
FROM payment_subscriptions ps
JOIN products_services prod ON ps.business_profile_id = prod.business_id
WHERE ps.business_profile_id = 'USER_ID'
GROUP BY plan_id;
```

### Check Webhook Events
```sql
SELECT 
  event_id,
  event_type,
  entity_id,
  status,
  processed_at
FROM processed_webhook_events
WHERE entity_id = 'SUBSCRIPTION_ID'
ORDER BY processed_at DESC;
```

## Automated Testing

Run the automated test suite:
```bash
# View test results in browser
curl http://localhost:3000/api/test/subscription-flow?format=html

# Get JSON results
curl http://localhost:3000/api/test/subscription-flow

# Test specific scenario
curl http://localhost:3000/api/test/subscription-flow?type=trial-auth
```

## Common Issues to Watch For

1. **Race Conditions**: Webhooks arriving out of order
2. **Idempotency**: Duplicate webhook processing
3. **State Inconsistency**: Database vs Razorpay status mismatch
4. **Payment Method Detection**: Incorrect flow selection
5. **Product Limits**: Not enforced after plan changes
6. **Original Plan Restoration**: Not working after resume

## Troubleshooting

### If tests fail:
1. Check Supabase logs for database errors
2. Check webhook event processing in `processed_webhook_events` table
3. Verify Razorpay webhook configuration
4. Check subscription state consistency with validation function:
   ```sql
   SELECT * FROM validate_and_fix_subscription_state('USER_ID');
   ```

### Reset test data:
```sql
-- Reset subscription data for testing
DELETE FROM payment_subscriptions WHERE business_profile_id = 'TEST_USER_ID';
UPDATE business_profiles 
SET has_active_subscription = false, trial_end_date = NOW() + INTERVAL '7 days'
WHERE id = 'TEST_USER_ID';
```
