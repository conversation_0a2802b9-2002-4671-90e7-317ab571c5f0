import { RAZORPAY_API_URL, getRazorpayApiHeaders } from "../utils/auth";

export interface PaymentDetails {
  id: string;
  amount: number;
  currency: string;
  status: string;
  method: string; // "upi", "netbanking", "card", etc.
  created_at: number;
  captured: boolean;
  description?: string;
  email?: string;
  contact?: string;
  notes?: Record<string, string>;
}

export interface PaymentResponse {
  success: boolean;
  data?: PaymentDetails;
  error?: string;
}

/**
 * Get payment details from Razorpay
 * @param paymentId The payment ID to fetch details for
 * @returns Payment details or error
 */
export async function getPaymentDetails(paymentId: string): Promise<PaymentResponse> {
  try {
    console.log(`[RAZORPAY_PAYMENT] Fetching payment details for ID: ${paymentId}`);

    // Get API headers
    const headers = getRazorpayApiHeaders();

    // Make API request
    const response = await fetch(`${RAZORPAY_API_URL.replace('/v2', '/v1')}/payments/${paymentId}`, {
      method: 'GET',
      headers
    });

    // Parse response
    const payment = await response.json();

    if (!response.ok) {
      console.error('[RAZORPAY_PAYMENT] Error fetching payment:', payment);
      return {
        success: false,
        error: payment.error?.description || "Failed to fetch payment details"
      };
    }

    console.log(`[RAZORPAY_PAYMENT] Payment details fetched successfully:`, {
      id: payment.id,
      method: payment.method,
      status: payment.status,
      amount: payment.amount,
      currency: payment.currency
    });

    return {
      success: true,
      data: {
        id: payment.id,
        amount: payment.amount,
        currency: payment.currency,
        status: payment.status,
        method: payment.method,
        created_at: payment.created_at,
        captured: payment.captured,
        description: payment.description,
        email: payment.email,
        contact: payment.contact,
        notes: payment.notes
      }
    };
  } catch (error) {
    console.error(`[RAZORPAY_PAYMENT] Error fetching payment details:`, error);

    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch payment details"
    };
  }
}

/**
 * Check if a payment method requires new subscription creation (UPI/netbanking)
 * instead of subscription update (card)
 * @param paymentMethod The payment method from Razorpay
 * @returns True if requires new subscription, false if can update existing
 */
export function requiresNewSubscription(paymentMethod: string): boolean {
  const method = paymentMethod.toLowerCase();
  return method === 'upi' || method === 'netbanking' || method === 'emandate' || method === 'enach';
}

/**
 * Get user-friendly payment method name
 * @param paymentMethod The payment method from Razorpay
 * @returns User-friendly name
 */
export function getPaymentMethodDisplayName(paymentMethod: string): string {
  const method = paymentMethod.toLowerCase();
  
  switch (method) {
    case 'upi':
      return 'UPI';
    case 'netbanking':
      return 'Net Banking';
    case 'card':
      return 'Card';
    case 'emandate':
    case 'enach':
      return 'eMandate';
    default:
      return paymentMethod;
  }
}
