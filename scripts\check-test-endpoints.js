#!/usr/bin/env node

/**
 * Check Test Endpoints Availability
 * 
 * This script checks if the test endpoints are available and properly secured.
 * It verifies:
 * 1. Development-only restriction
 * 2. Authentication bypass functionality
 * 3. Endpoint responsiveness
 */

const http = require('http');

function makeRequest(url, timeout = 10000) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    
    const req = http.request({
      hostname: urlObj.hostname,
      port: urlObj.port || 80,
      path: urlObj.pathname + urlObj.search,
      method: 'GET',
      timeout: timeout
    }, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          resolve({ status: res.statusCode, data: result });
        } catch (_error) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', (error) => {
      if (error.code === 'ECONNREFUSED') {
        reject(new Error('Connection refused - development server not running'));
      } else {
        reject(error);
      }
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
}

async function checkEndpoints() {
  console.log('🔍 Checking Test Endpoint Availability...\n');

  const baseUrl = 'http://localhost:3000';
  
  // Test endpoints to check
  const endpoints = [
    {
      name: 'Subscription Flow Tests',
      url: `${baseUrl}/api/test/subscription-flow?bypass=internal-testing`,
      description: 'Unit tests for subscription logic'
    },
    {
      name: 'Subscription Scenarios',
      url: `${baseUrl}/api/test/subscription-scenarios?bypass=internal-testing`,
      description: 'E2E webhook simulation tests'
    }
  ];

  let allAvailable = true;

  for (const endpoint of endpoints) {
    console.log(`📋 Checking: ${endpoint.name}`);
    console.log(`   URL: ${endpoint.url}`);
    console.log(`   Description: ${endpoint.description}`);

    try {
      const response = await makeRequest(endpoint.url);
      
      if (response.status === 200) {
        console.log(`✅ Available - Status: ${response.status}`);
        
        if (response.data && response.data.success) {
          console.log(`   Response: Success`);
          if (response.data.data) {
            console.log(`   Data available: Yes`);
          }
        } else {
          console.log(`   Response: ${JSON.stringify(response.data).substring(0, 100)}...`);
        }
      } else if (response.status === 403) {
        console.log(`🔒 Properly secured - Status: ${response.status}`);
        console.log(`   This is expected in production mode`);
      } else {
        console.log(`⚠️  Unexpected status: ${response.status}`);
        console.log(`   Response: ${JSON.stringify(response.data).substring(0, 100)}...`);
        allAvailable = false;
      }
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      allAvailable = false;
      
      if (error.message.includes('Connection refused')) {
        console.log(`   💡 Hint: Start the development server with 'npm run dev'`);
      }
    }
    
    console.log('');
  }

  // Test authentication security
  console.log('🔐 Testing Authentication Security...\n');
  
  const securityTest = `${baseUrl}/api/test/subscription-flow`; // Without bypass parameter
  console.log(`📋 Testing endpoint without bypass parameter:`);
  console.log(`   URL: ${securityTest}`);
  
  try {
    const response = await makeRequest(securityTest);
    
    if (response.status === 401) {
      console.log(`✅ Properly secured - Status: ${response.status}`);
      console.log(`   Authentication required as expected`);
    } else if (response.status === 403) {
      console.log(`✅ Properly secured - Status: ${response.status}`);
      console.log(`   Development-only restriction working`);
    } else {
      console.log(`⚠️  Security concern - Status: ${response.status}`);
      console.log(`   Expected 401 or 403, got ${response.status}`);
    }
  } catch (error) {
    console.log(`❌ Error testing security: ${error.message}`);
  }

  console.log('\n📊 Summary:');
  console.log('═'.repeat(40));
  
  if (allAvailable) {
    console.log('✅ All test endpoints are available and properly secured');
    console.log('🎯 Ready to run comprehensive subscription flow tests');
    console.log('\n💡 To run full tests, use:');
    console.log('   node scripts/test-all-subscription-scenarios.js');
  } else {
    console.log('❌ Some endpoints are not available');
    console.log('💡 Make sure the development server is running:');
    console.log('   npm run dev');
  }
}

// Run check if this script is executed directly
if (require.main === module) {
  checkEndpoints().catch(error => {
    console.error('❌ Endpoint check failed:', error);
    process.exit(1);
  });
}

module.exports = { checkEndpoints, makeRequest };
