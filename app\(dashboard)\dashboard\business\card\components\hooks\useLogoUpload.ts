"use client";

import { useState, useTransition } from "react";
import { toast } from "sonner";
import { UseFormReturn } from "react-hook-form";
import { BusinessCardData } from "../../schema";
import { uploadLogoAndGetUrl, updateLogoUrl, deleteLogoUrl } from "../../actions";
import { compressImageUltraAggressiveClient } from "@/lib/utils/client-image-compression";

export type LogoUploadStatus = "idle" | "uploading" | "success" | "error";

interface UseLogoUploadOptions {
  form: UseFormReturn<BusinessCardData>;
  initialLogoUrl?: string;
  onUpdateCardData: (_data: Partial<BusinessCardData>) => void;
}

export function useLogoUpload({
  form,
  initialLogoUrl = "",
  onUpdateCardData
}: UseLogoUploadOptions) {
  const [logoUploadStatus, setLogoUploadStatus] = useState<LogoUploadStatus>("idle");
  const [logoUploadError, setLogoUploadError] = useState<string | null>(null);
  const [localPreviewUrl, setLocalPreviewUrl] = useState<string | null>(null);
  const [isLogoUploading, startLogoUploadTransition] = useTransition();
  const [imageToCrop, setImageToCrop] = useState<string | null>(null);
  const [originalFile, setOriginalFile] = useState<File | null>(null);

  // File selection handler
  const onFileSelect = (file: File | null) => {
    if (localPreviewUrl) {
      URL.revokeObjectURL(localPreviewUrl);
      setLocalPreviewUrl(null);
    }

    if (file) {
      if (file.size > 15 * 1024 * 1024) {
        toast.error("Image too large", {
          description: "Please select an image smaller than 15MB"
        });
        form.setValue("logo_url", null, { shouldDirty: true });
        onUpdateCardData({ logo_url: null });
        setLogoUploadStatus("idle");
        setLogoUploadError("File size must be less than 15MB.");
        setLocalPreviewUrl(null);
        return;
      }

      // Prepare for cropping
      setOriginalFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImageToCrop(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      form.setValue("logo_url", null, { shouldDirty: true });
      onUpdateCardData({ logo_url: null });
      setLogoUploadStatus("idle");
      setLogoUploadError(null);
      setLocalPreviewUrl(null);
    }
  };

  // Logo upload handler
  const handleLogoUpload = async (file: File) => {
    setLogoUploadStatus("uploading");
    setLogoUploadError(null);

    startLogoUploadTransition(async () => {
      try {
        // Compress image on client-side first
        const compressionResult = await compressImageUltraAggressiveClient(file, {
          maxDimension: 500,
          targetSizeKB: 60
        });

        // Convert blob to file for upload
        const compressedFile = new File([compressionResult.blob], file.name, {
          type: compressionResult.blob.type
        });

        const formData = new FormData();
        formData.append("logoFile", compressedFile);

        const result = await uploadLogoAndGetUrl(formData);

        if (result.success && result.url) {
          const newLogoUrl = result.url;

          // Update form and preview
          form.setValue("logo_url", newLogoUrl, { shouldDirty: true, shouldTouch: true });
          onUpdateCardData({ logo_url: newLogoUrl });
          setLogoUploadStatus("success");

          // Clean up preview URL
          setLocalPreviewUrl(null);
          if (localPreviewUrl) URL.revokeObjectURL(localPreviewUrl);

          toast.success("Logo uploaded successfully!");

          // Save URL to DB immediately
          try {
            const updateResult = await updateLogoUrl(newLogoUrl);
            if (!updateResult.success) {
              toast.error(`Logo uploaded, but failed to save URL: ${updateResult.error}`);
            }

            // Update form state after successful DB save
            if (updateResult.success) {
              onUpdateCardData({ logo_url: newLogoUrl });
              form.reset({ ...form.getValues(), logo_url: newLogoUrl });
            }
          } catch (err) {
            console.error("Error saving logo URL:", err);
            toast.error("Error saving logo URL after upload.");
          }
        } else {
          setLogoUploadStatus("error");
          const errorMessage = result.error || "Failed to upload logo.";
          setLogoUploadError(errorMessage);
          setLocalPreviewUrl(null);
          if (localPreviewUrl) URL.revokeObjectURL(localPreviewUrl);

          // Show user-friendly error message
          if (errorMessage.includes("File size must be less than 15MB")) {
            toast.error("Image too large", {
              description: "Please select an image smaller than 15MB"
            });
          } else if (errorMessage.includes("Invalid file type")) {
            toast.error("Invalid file type", {
              description: "Please select a JPG, PNG, WebP, or GIF image"
            });
          } else {
            toast.error("Upload failed", {
              description: errorMessage
            });
          }

          form.setValue("logo_url", initialLogoUrl || "", { shouldDirty: false });
        }
      } catch (compressionError) {
        console.error("Image compression failed:", compressionError);
        setLogoUploadStatus("error");
        setLogoUploadError("Failed to process image. Please try a different image.");
        toast.error("Image processing failed", {
          description: "Please try a different image or reduce the file size."
        });
      }
    });
  };

  // Handle crop completion
  const handleCropComplete = (croppedBlob: Blob | null) => {
    setImageToCrop(null); // Close dialog

    if (croppedBlob && originalFile) {
      // Convert blob to file and upload
      const croppedFile = new File([croppedBlob], originalFile.name, { type: 'image/webp' });
      const previewUrl = URL.createObjectURL(croppedFile);
      setLocalPreviewUrl(previewUrl);
      handleLogoUpload(croppedFile);
    } else {
      // Handle crop cancellation or error
      console.log("Cropping cancelled or failed.");
      setOriginalFile(null);
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      if (fileInput) fileInput.value = "";
    }
  };

  // Handle crop dialog close
  const handleCropDialogClose = () => {
    setImageToCrop(null);
    setOriginalFile(null);
    // Clear the file input
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    if (fileInput) fileInput.value = "";
  };

  // State for delete confirmation dialog
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Open delete confirmation dialog
  const openDeleteDialog = () => {
    if (!form.getValues("logo_url")) {
      return; // No logo to delete
    }
    setIsDeleteDialogOpen(true);
  };

  // Close delete confirmation dialog
  const closeDeleteDialog = () => {
    if (!isDeleting) {
      setIsDeleteDialogOpen(false);
    }
  };

  // Handle logo deletion after confirmation
  const confirmLogoDelete = async () => {
    setIsDeleting(true);
    setLogoUploadStatus("uploading"); // Reuse the uploading status for deletion
    setLogoUploadError(null);

    startLogoUploadTransition(async () => {
      try {
        const result = await deleteLogoUrl();

        if (result.success) {
          // Update form and preview with null instead of empty string
          form.setValue("logo_url", null, { shouldDirty: true, shouldTouch: true });
          onUpdateCardData({ logo_url: null });
          setLogoUploadStatus("idle");

          // Clean up preview URL
          setLocalPreviewUrl(null);
          if (localPreviewUrl) URL.revokeObjectURL(localPreviewUrl);

          // Reset file input to ensure it can be used again
          const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
          if (fileInput) fileInput.value = "";

          toast.success("Logo deleted successfully!");

          // Reset form with updated values
          form.reset({ ...form.getValues(), logo_url: null });
        } else {
          setLogoUploadStatus("error");
          setLogoUploadError(result.error || "Failed to delete logo.");
          toast.error(result.error || "Failed to delete logo.");
        }
      } catch (err) {
        console.error("Error deleting logo:", err);
        setLogoUploadStatus("error");
        setLogoUploadError("An unexpected error occurred while deleting the logo.");
        toast.error("An unexpected error occurred while deleting the logo.");
      } finally {
        setIsDeleting(false);
        setIsDeleteDialogOpen(false);
      }
    });
  };

  // Logo error display component
  const logoErrorDisplay = logoUploadStatus === "error" && logoUploadError ? logoUploadError : null;

  return {
    logoUploadStatus,
    logoUploadError,
    localPreviewUrl,
    isLogoUploading,
    imageToCrop,
    onFileSelect,
    handleLogoUpload,
    handleCropComplete,
    handleCropDialogClose,
    handleLogoDelete: openDeleteDialog, // Renamed for backward compatibility
    logoErrorDisplay,
    isDeleteDialogOpen,
    isDeleting,
    openDeleteDialog,
    closeDeleteDialog,
    confirmLogoDelete
  };
}
