"use client";

// Removed unused imports as UPI warning dialog is no longer needed
import SimplifiedPlanActionDialog from "./SimplifiedPlanActionDialog";
import FirstTimePaidPlanDialog from "./FirstTimePaidPlanDialog";
// Removed UpiPaymentMethodWarning import as it's no longer needed
import { PricingPlan } from "@/lib/PricingPlans";
import { SubscriptionStatus } from "../page";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";

interface DialogManagerProps {
  dialogPlan: PricingPlan | null;
  isPlanDialogOpen: boolean;
  isFirstTimePaidPlanDialogOpen: boolean;
  // Removed UPI warning dialog props as they're no longer needed
  dialogLoading: boolean;
  billingCycle: "monthly" | "yearly";
  subscriptionStatus: SubscriptionStatus;
  trialEndDate: string | null;
  lastPaymentMethod?: string | null;

  // Setters
  setIsPlanDialogOpen: (_open: boolean) => void;
  setIsFirstTimePaidPlanDialogOpen: (_open: boolean) => void;
  // Removed UPI warning dialog setters
  setDialogLoading: (_loading: boolean) => void;
  setActiveTab: (_tab: "plans" | "subscription") => void;

  // Functions
  handleDialogSubscribe: () => Promise<void>;
  handleActivateTrial: () => Promise<void>;
  resetProcessing: () => void;
  startProcessing: (_message: string) => void;
  completeProcessing: (_success: boolean, _message?: string) => void;
  setSubscriptionCreated: (_message: string) => void;
  setFuturePaymentAuthorized: (_message: string) => void;
  router: AppRouterInstance;
}

export default function DialogManager({
  dialogPlan,
  isPlanDialogOpen,
  isFirstTimePaidPlanDialogOpen,
  // Removed UPI warning dialog props
  dialogLoading,
  billingCycle,
  subscriptionStatus,
  trialEndDate,
  lastPaymentMethod: _lastPaymentMethod, // Not used anymore
  setIsPlanDialogOpen,
  setIsFirstTimePaidPlanDialogOpen,
  // Removed UPI warning dialog setters
  setDialogLoading,
  setActiveTab,
  handleDialogSubscribe,
  handleActivateTrial,
  resetProcessing,
  startProcessing: _startProcessing, // Not used anymore
  completeProcessing: _completeProcessing, // Not used anymore
  setSubscriptionCreated: _setSubscriptionCreated, // Not used anymore
  setFuturePaymentAuthorized: _setFuturePaymentAuthorized, // Not used anymore
  router: _router, // Not used anymore
}: DialogManagerProps) {

  // Removed UPI warning dialog handlers as they're no longer needed

  return (
    <>
      {/* Plan Action Dialog */}
      {dialogPlan && (
        <SimplifiedPlanActionDialog
          isOpen={isPlanDialogOpen}
          onClose={() => {
            setIsPlanDialogOpen(false);
            setDialogLoading(false); // Reset loading state when dialog is closed
            resetProcessing(); // Also reset the processing state to ensure no lingering toast notifications
          }}
          plan={dialogPlan}
          trialEndDate={
            subscriptionStatus === "authenticated" ? null : trialEndDate
          }
          _onSubscribe={handleDialogSubscribe}
          isLoading={dialogLoading} // Pass loading state to dialog
        />
      )}

      {/* First Time Paid Plan Dialog */}
      {dialogPlan && (
        <FirstTimePaidPlanDialog
          isOpen={isFirstTimePaidPlanDialogOpen}
          onClose={() => {
            setIsFirstTimePaidPlanDialogOpen(false);
            setDialogLoading(false); // Reset loading state when dialog is closed
            resetProcessing(); // Also reset the processing state to ensure no lingering toast notifications
          }}
          plan={dialogPlan}
          billingCycle={billingCycle} // Pass the selected billing cycle
          onActivateTrial={handleActivateTrial}
          isLoading={dialogLoading} // Pass loading state to dialog
        />
      )}

      {/* Removed UPI Payment Method Warning Dialog as it's no longer needed */}
    </>
  );
}
