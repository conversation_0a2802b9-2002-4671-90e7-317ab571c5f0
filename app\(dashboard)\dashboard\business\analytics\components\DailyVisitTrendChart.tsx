"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { BarChart3 } from "lucide-react";
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from "recharts";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useIsMobile } from "@/hooks/use-mobile";
import { formatIndianNumberShort } from "@/lib/utils";
import PremiumFeatureLock from "./PremiumFeatureLock";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";

interface DailyVisitTrendChartProps {
  trend7Days: { date: string; visits: number }[];
  trend30Days: { date: string; visits: number }[];
  userPlan?: string | null;
}

const chartConfig = {
  visits: {
    label: "Visits",
    color: "var(--chart-1)",
  },
} satisfies ChartConfig;

export default function DailyVisitTrendChart({
  trend7Days,
  trend30Days,
  userPlan,
}: DailyVisitTrendChartProps) {
  const isMobile = useIsMobile();
  const [selectedRange, setSelectedRange] = useState<"7days" | "30days">("7days");

  // Format date string (YYYY-MM-DD) to a more readable format (DD MMM)
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString("en-IN", {
      day: "2-digit",
      month: "short",
    });
  };

  // Format date for X-axis ticks - shorter on mobile
  const formatXAxisTick = (dateStr: string) => {
    if (isMobile) {
      const date = new Date(dateStr);
      return date.getDate().toString(); // Just the day number on mobile
    }
    return dateStr; // Full formatted date on desktop
  };

  // Generate all dates for the selected range
  const generateAllDates = (days: number) => {
    const dates = [];
    const today = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(today.getDate() - i);
      const dateStr = date.toISOString().split('T')[0]; // YYYY-MM-DD format
      dates.push(dateStr);
    }

    return dates;
  };

  // Get all dates for the selected range
  const allDates = generateAllDates(selectedRange === "7days" ? 7 : 30);

  // Create a map of existing data
  const dataMap = new Map();

  if (selectedRange === "7days") {
    trend7Days.forEach(item => {
      dataMap.set(item.date, item.visits);
    });
  } else {
    trend30Days.forEach(item => {
      dataMap.set(item.date, item.visits);
    });
  }

  // Create chart data with all dates, using 0 for missing data
  const chartData = allDates.map(date => ({
    date,
    visits: dataMap.has(date) ? dataMap.get(date) : 0,
    formattedDate: formatDate(date),
  }));

  // Calculate the maximum value for better Y-axis scaling
  const maxVisits = Math.max(...chartData.map(item => item.visits));
  // Calculate a nice rounded upper bound for the Y-axis
  const calculateYAxisMax = (maxValue: number) => {
    if (maxValue <= 0) return 5; // Default if no data
    if (maxValue <= 5) return Math.ceil(maxValue * 1.2); // Add 20% padding for small values
    if (maxValue <= 10) return Math.ceil(maxValue * 1.1); // Add 10% padding for medium values
    return Math.ceil(maxValue * 1.05); // Add 5% padding for large values
  };
  const yAxisMax = calculateYAxisMax(maxVisits);





  // Custom formatter for Y-axis ticks using Indian number format
  const formatYAxisTick = (value: number) => {
    return formatIndianNumberShort(Math.floor(value));
  };

  // Handle range change
  const handleRangeChange = (value: string) => {
    setSelectedRange(value as "7days" | "30days");
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  // Check if user has access to this premium feature
  const isPremiumUser = userPlan === "growth" || userPlan === "pro" || userPlan === "enterprise";

  // If user doesn't have a premium plan, show the premium feature lock component
  if (!isPremiumUser) {
    return (
      <PremiumFeatureLock
        title="Daily Visit Trend"
        description="Upgrade to Growth plan or higher to see detailed daily visit trends for your business."
      />
    );
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="rounded-lg border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black p-3 sm:p-4 md:p-6 shadow-sm"
    >
      <div className="mb-4">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
          <h3 className="text-base sm:text-lg font-semibold text-neutral-900 dark:text-neutral-100">
            Daily Visit Trend
          </h3>
          <div className="flex items-center space-x-2 text-xs sm:text-sm text-neutral-500 dark:text-neutral-400">
            <BarChart3 className="h-3 w-3 sm:h-4 sm:w-4" />
            <span>Unique visitors by day</span>
          </div>
        </div>
      </div>

      {/* Range Selection Controls */}
      <div className="mb-4 flex items-center justify-center sm:justify-between">
        <div className="hidden sm:block sm:flex-1"></div>
        <Select value={selectedRange} onValueChange={handleRangeChange}>
          <SelectTrigger className={isMobile ? "w-full" : "w-[180px]"}>
            <SelectValue placeholder="Select Range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7days">Last 7 Days</SelectItem>
            <SelectItem value="30days">Last 30 Days</SelectItem>
          </SelectContent>
        </Select>
        <div className="hidden sm:block sm:flex-1"></div>
      </div>

      {/* Chart */}
      <div className="h-[250px] sm:h-[300px] w-full px-1 pb-2">
        <ChartContainer config={chartConfig} className="h-full w-full">
          <AreaChart
            data={chartData}
            margin={isMobile
              ? { top: 5, right: 5, left: 0, bottom: 5 }
              : { top: 10, right: 10, left: 0, bottom: 5 }
            }
          >
            <defs>
              <linearGradient id="fillVisits" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-visits)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-visits)"
                  stopOpacity={0.1}
                />
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" vertical={false} />
            <XAxis
              dataKey="formattedDate"
              axisLine={false}
              tickLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={formatXAxisTick}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              domain={[0, yAxisMax]}
              allowDecimals={false}
              tickFormatter={formatYAxisTick}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  labelFormatter={(label) => `Date: ${label}`}
                  formatter={(value) => [
                    formatIndianNumberShort(Number(value)),
                    " Visits"
                  ]}
                />
              }
            />
            <Area
              dataKey="visits"
              type="natural"
              fill="url(#fillVisits)"
              stroke="var(--color-visits)"
              strokeWidth={isMobile ? 1.5 : 2}
            />
          </AreaChart>
        </ChartContainer>
      </div>
    </motion.div>
  );
}
