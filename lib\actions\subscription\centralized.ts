/**
 * CENTRALIZED SUBSCRIPTION ACTION
 * 
 * This is the single entry point for all subscription operations.
 * It uses the SubscriptionFlowManager to determine the correct flow
 * and executes the appropriate action based on business rules.
 */

"use server";

import { createClient } from "@/utils/supabase/server";
import { SubscriptionFlowManager } from "@/lib/subscription/SubscriptionFlowManager";
import {
  SubscriptionRequest,
  SubscriptionFlowResult,
  SubscriptionContext
} from "@/lib/subscription/types";

/**
 * MASTER SUBSCRIPTION FUNCTION
 * Handles all subscription scenarios with centralized logic
 */
export async function processSubscription(
  planId: string,
  planCycle: 'monthly' | 'yearly',
  paymentMethod?: string
): Promise<SubscriptionFlowResult> {
  try {
    // Get authenticated user
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        success: false,
        paymentRequired: false,
        message: 'Authentication required',
        error: 'User not authenticated'
      };
    }

    // Get current subscription context
    const context = await getSubscriptionContext(user.id);
    if (!context) {
      return {
        success: false,
        paymentRequired: false,
        message: 'Failed to get user context',
        error: 'Could not retrieve user subscription context'
      };
    }

    // Create subscription request
    const request: SubscriptionRequest = {
      planId: planId as 'free' | 'basic' | 'growth' | 'pro' | 'enterprise',
      planCycle,
      context,
      paymentMethod
    };

    // Validate request
    const validation = SubscriptionFlowManager.validateRequest(request);
    if (!validation.valid) {
      return {
        success: false,
        paymentRequired: false,
        message: 'Invalid subscription request',
        error: validation.error
      };
    }

    // Determine subscription flow
    const decision = SubscriptionFlowManager.determineSubscriptionFlow(request);

    console.log(`[CENTRALIZED_SUBSCRIPTION] Flow decision:`, {
      flowType: decision.flowType,
      reason: decision.reason,
      paymentMethod: request.paymentMethod,
      context: {
        hasRazorpaySubscription: Boolean(context.razorpaySubscriptionId),
        lastPaymentMethod: context.lastPaymentMethod
      }
    });

    // Execute the appropriate flow
    switch (decision.flowType) {
      case 'UPFRONT_PAYMENT':
        return await handleUpfrontPaymentFlow(request, decision);
      
      case 'UPDATE_SUBSCRIPTION':
        return await handleUpdateSubscriptionFlow(request, decision);
      
      case 'CREATE_AND_CANCEL':
        return await handleCreateAndCancelFlow(request, decision);
      
      case 'FRESH_SUBSCRIPTION':
        return await handleFreshSubscriptionFlow(request, decision);
      
      default:
        return {
          success: false,
          paymentRequired: false,
          message: 'Unknown subscription flow',
          error: 'Unsupported subscription flow type'
        };
    }

  } catch (error) {
    console.error('Subscription processing failed:', error);
    return {
      success: false,
      paymentRequired: false,
      message: 'Subscription processing failed',
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Get current subscription context for the user
 */
async function getSubscriptionContext(userId: string): Promise<SubscriptionContext | null> {
  try {
    const supabase = await createClient();

    // Get business profile
    const { data: profile, error: profileError } = await supabase
      .from('business_profiles')
      .select('id, trial_end_date, has_active_subscription, status')
      .eq('id', userId)
      .single();

    if (profileError) {
      console.error('Profile error:', profileError);
      return null;
    }

    // Get payment subscription
    const { data: subscription, error: subscriptionError } = await supabase
      .from('payment_subscriptions')
      .select(`
        id,
        business_profile_id,
        razorpay_subscription_id,
        subscription_status,
        plan_id,
        plan_cycle,
        last_payment_method,
        subscription_start_date,
        subscription_expiry_time,
        cancelled_at
      `)
      .eq('business_profile_id', userId)
      .single();

    // Note: subscription might not exist for new users, that's okay
    if (subscriptionError && subscriptionError.code !== 'PGRST116') {
      console.error('Subscription error:', subscriptionError);
    }

    return {
      userId,
      currentPlanId: subscription?.plan_id,
      currentPlanCycle: subscription?.plan_cycle as 'monthly' | 'yearly' | undefined,
      subscriptionStatus: subscription?.subscription_status,
      trialEndDate: profile.trial_end_date,
      razorpaySubscriptionId: subscription?.razorpay_subscription_id,
      lastPaymentMethod: subscription?.last_payment_method,
      hasActiveSubscription: profile.has_active_subscription
    };

  } catch (error) {
    console.error('Error getting subscription context:', error);
    return null;
  }
}

/**
 * Handle upfront payment flow (post-trial, no active subscription)
 */
async function handleUpfrontPaymentFlow(
  request: SubscriptionRequest,
  _decision: unknown
): Promise<SubscriptionFlowResult> {
  try {
    // Use server-side subscription creation
    const { createSubscription } = await import('@/lib/actions/subscription/create');
    const result = await createSubscription(request.planId, request.planCycle);

    return {
      success: result.success,
      subscriptionId: (typeof result.data?.subscription_id === 'string' ? result.data.subscription_id : undefined),
      paymentRequired: true,
      message: result.success
        ? 'Subscription created successfully. Payment authorization required.'
        : 'Failed to create subscription',
      error: result.error,
      data: result.data
    };
  } catch (error) {
    console.error('Upfront payment flow error:', error);
    return {
      success: false,
      subscriptionId: undefined,
      paymentRequired: false,
      message: 'Failed to create subscription',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Handle update subscription flow (card payment)
 * For card payments, we create a new subscription to get Razorpay authorization
 * but handle the cancellation after payment confirmation
 */
async function handleUpdateSubscriptionFlow(
  request: SubscriptionRequest,
  _decision: unknown
): Promise<SubscriptionFlowResult> {
  try {
    // For card payments, we still need to create a new subscription to get user authorization
    // The difference from UPI/netbanking is that we can update the existing subscription
    // after the new one is authorized, rather than cancelling it

    if (!request.context.razorpaySubscriptionId) {
      console.error('Missing razorpaySubscriptionId in context for card update flow');
      return {
        success: false,
        subscriptionId: undefined,
        paymentRequired: false,
        message: 'Failed to process plan change: Current subscription ID not found.',
        error: 'Missing razorpaySubscriptionId in context'
      };
    }

    // Use the same creation logic as create/cancel but mark it as a card update
    const { cancelAndCreateSubscription } = await import('@/lib/actions/subscription/create');
    const result = await cancelAndCreateSubscription(
      request.context.razorpaySubscriptionId,
      request.planId,
      request.planCycle,
      'card_update' // Special flag to indicate this is a card update, not UPI/netbanking
    );

    return {
      success: result.success,
      subscriptionId: (typeof result.data?.subscription_id === 'string' ? result.data.subscription_id : undefined),
      paymentRequired: true, // Card updates also require payment authorization
      message: result.success
        ? 'New subscription created. Payment authorization required to update your plan.'
        : 'Failed to create new subscription',
      error: result.error,
      data: result.data
    };
  } catch (error) {
    console.error('Update subscription flow error:', error);
    return {
      success: false,
      subscriptionId: undefined,
      paymentRequired: false,
      message: 'Failed to update subscription',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Handle create and cancel flow (UPI/emandate)
 */
async function handleCreateAndCancelFlow(
  request: SubscriptionRequest,
  _decision: unknown
): Promise<SubscriptionFlowResult> {
  if (!request.context.razorpaySubscriptionId) {
    console.error('Missing razorpaySubscriptionId in context for create and cancel flow');
    return {
      success: false,
      subscriptionId: undefined,
      paymentRequired: false,
      message: 'Failed to process plan change: Current subscription ID not found.',
      error: 'Missing razorpaySubscriptionId in context'
    };
  }

  try {
    // Use server-side subscription creation that also handles cancelling the old one
    const { cancelAndCreateSubscription } = await import('@/lib/actions/subscription/create');
    const result = await cancelAndCreateSubscription(
      request.context.razorpaySubscriptionId,
      request.planId,
      request.planCycle
    );

    return {
      success: result.success,
      subscriptionId: (typeof result.data?.subscription_id === 'string' ? result.data.subscription_id : undefined),
      paymentRequired: true,
      message: result.success
        ? 'New subscription created. Your current subscription will be cancelled after activation.'
        : 'Failed to create new subscription',
      error: result.error,
      data: result.data
    };
  } catch (error) {
    console.error('Create and cancel flow error:', error);
    return {
      success: false,
      subscriptionId: undefined,
      paymentRequired: false,
      message: 'Failed to create new subscription',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Handle fresh subscription flow (new users or trial users)
 */
async function handleFreshSubscriptionFlow(
  request: SubscriptionRequest,
  decision: { requiresUpfrontPayment: boolean; paymentTiming: string }
): Promise<SubscriptionFlowResult> {
  try {
    // Use server-side subscription creation
    const { createSubscription } = await import('@/lib/actions/subscription/create');
    const result = await createSubscription(request.planId, request.planCycle);

    return {
      success: result.success,
      subscriptionId: (typeof result.data?.id === 'string' ? result.data.id : (typeof result.data?.subscription_id === 'string' ? result.data.subscription_id : undefined)),
      paymentRequired: decision.requiresUpfrontPayment,
      message: result.success
        ? (decision.paymentTiming === 'TRIAL_END'
            ? 'Subscription created. Payment will be processed when trial ends.'
            : 'Subscription created successfully.')
        : 'Failed to create subscription',
      error: result.error,
      data: result.data
    };
  } catch (error) {
    console.error('Fresh subscription flow error:', error);
    return {
      success: false,
      subscriptionId: undefined,
      paymentRequired: false,
      message: 'Failed to create subscription',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
