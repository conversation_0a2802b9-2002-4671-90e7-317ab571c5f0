/**
 * CENTRALIZED SUBSCRIPTION ACTION
 * 
 * This is the single entry point for all subscription operations.
 * It uses the SubscriptionFlowManager to determine the correct flow
 * and executes the appropriate action based on business rules.
 */

"use server";

import { createClient } from "@/utils/supabase/server";
import { SubscriptionFlowManager } from "@/lib/subscription/SubscriptionFlowManager";
import {
  SubscriptionRequest,
  SubscriptionFlowResult,
  SubscriptionContext
} from "@/lib/subscription/types";

/**
 * MASTER SUBSCRIPTION FUNCTION
 * Handles all subscription scenarios with centralized logic
 */
export async function processSubscription(
  planId: string,
  planCycle: 'monthly' | 'yearly',
  paymentMethod?: string
): Promise<SubscriptionFlowResult> {
  try {
    // Get authenticated user
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        success: false,
        paymentRequired: false,
        message: 'Authentication required',
        error: 'User not authenticated'
      };
    }

    // Get current subscription context
    const context = await getSubscriptionContext(user.id);
    if (!context) {
      return {
        success: false,
        paymentRequired: false,
        message: 'Failed to get user context',
        error: 'Could not retrieve user subscription context'
      };
    }

    // Create subscription request
    const request: SubscriptionRequest = {
      planId: planId as 'free' | 'basic' | 'growth' | 'pro' | 'enterprise',
      planCycle,
      context,
      paymentMethod
    };

    // Validate request
    const validation = SubscriptionFlowManager.validateRequest(request);
    if (!validation.valid) {
      return {
        success: false,
        paymentRequired: false,
        message: 'Invalid subscription request',
        error: validation.error
      };
    }

    // Determine subscription flow
    const decision = SubscriptionFlowManager.determineSubscriptionFlow(request);

    console.log(`[CENTRALIZED_SUBSCRIPTION] Flow decision:`, {
      flowType: decision.flowType,
      reason: decision.reason,
      paymentMethod: request.paymentMethod,
      context: {
        hasRazorpaySubscription: Boolean(context.razorpaySubscriptionId),
        lastPaymentMethod: context.lastPaymentMethod
      }
    });

    // Execute the appropriate flow
    switch (decision.flowType) {
      case 'UPFRONT_PAYMENT':
        return await handleUpfrontPaymentFlow(request, decision);
      
      case 'UPDATE_SUBSCRIPTION':
        return await handleUpdateSubscriptionFlow(request, decision);
      
      case 'CREATE_AND_CANCEL':
        return await handleCreateAndCancelFlow(request, decision);
      
      case 'FRESH_SUBSCRIPTION':
        return await handleFreshSubscriptionFlow(request, decision);
      
      default:
        return {
          success: false,
          paymentRequired: false,
          message: 'Unknown subscription flow',
          error: 'Unsupported subscription flow type'
        };
    }

  } catch (error) {
    console.error('Subscription processing failed:', error);
    return {
      success: false,
      paymentRequired: false,
      message: 'Subscription processing failed',
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Get current subscription context for the user
 */
async function getSubscriptionContext(userId: string): Promise<SubscriptionContext | null> {
  try {
    const supabase = await createClient();

    // Get business profile
    const { data: profile, error: profileError } = await supabase
      .from('business_profiles')
      .select('id, trial_end_date, has_active_subscription, status')
      .eq('id', userId)
      .single();

    if (profileError) {
      console.error('Profile error:', profileError);
      return null;
    }

    // Get payment subscription
    const { data: subscription, error: subscriptionError } = await supabase
      .from('payment_subscriptions')
      .select(`
        id,
        business_profile_id,
        razorpay_subscription_id,
        subscription_status,
        plan_id,
        plan_cycle,
        last_payment_method,
        subscription_start_date,
        subscription_expiry_time,
        cancelled_at
      `)
      .eq('business_profile_id', userId)
      .single();

    // Note: subscription might not exist for new users, that's okay
    if (subscriptionError && subscriptionError.code !== 'PGRST116') {
      console.error('Subscription error:', subscriptionError);
    }

    return {
      userId,
      currentPlanId: subscription?.plan_id,
      currentPlanCycle: subscription?.plan_cycle as 'monthly' | 'yearly' | undefined,
      subscriptionStatus: subscription?.subscription_status,
      trialEndDate: profile.trial_end_date,
      razorpaySubscriptionId: subscription?.razorpay_subscription_id,
      lastPaymentMethod: subscription?.last_payment_method,
      hasActiveSubscription: profile.has_active_subscription
    };

  } catch (error) {
    console.error('Error getting subscription context:', error);
    return null;
  }
}

/**
 * Handle upfront payment flow (post-trial, no active subscription)
 */
async function handleUpfrontPaymentFlow(
  request: SubscriptionRequest,
  _decision: unknown
): Promise<SubscriptionFlowResult> {
  try {
    // Use server-side subscription creation
    const { createSubscription } = await import('@/lib/actions/subscription/create');
    const result = await createSubscription(request.planId, request.planCycle);

    return {
      success: result.success,
      subscriptionId: (typeof result.data?.subscription_id === 'string' ? result.data.subscription_id : undefined),
      paymentRequired: true,
      message: result.success
        ? 'Subscription created successfully. Payment authorization required.'
        : 'Failed to create subscription',
      error: result.error,
      data: result.data
    };
  } catch (error) {
    console.error('Upfront payment flow error:', error);
    return {
      success: false,
      subscriptionId: undefined,
      paymentRequired: false,
      message: 'Failed to create subscription',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Handle update subscription flow (card payment)
 * For card payments, we use Razorpay's direct subscription update API
 * which requires user authorization via Razorpay modal
 */
async function handleUpdateSubscriptionFlow(
  request: SubscriptionRequest,
  _decision: unknown
): Promise<SubscriptionFlowResult> {
  try {
    if (!request.context.razorpaySubscriptionId) {
      console.error('Missing razorpaySubscriptionId in context for card update flow');
      return {
        success: false,
        subscriptionId: undefined,
        paymentRequired: false,
        message: 'Failed to process plan change: Current subscription ID not found.',
        error: 'Missing razorpaySubscriptionId in context'
      };
    }

    // For card payments, we use Razorpay's subscription update API
    // This requires user authorization and opens Razorpay modal
    const { updateSubscription } = await import('@/lib/razorpay/services/subscription');
    const { getRazorpayPlanId } = await import('@/lib/config/plans');

    // Get the Razorpay plan ID
    const razorpayPlanId = getRazorpayPlanId(
      request.planId as 'free' | 'basic' | 'growth' | 'pro' | 'enterprise',
      request.planCycle
    );

    if (!razorpayPlanId) {
      return {
        success: false,
        subscriptionId: undefined,
        paymentRequired: false,
        message: 'Invalid plan configuration',
        error: 'Could not find Razorpay plan ID'
      };
    }

    // Prepare update parameters
    const updateParams: any = {
      plan_id: razorpayPlanId,
      schedule_change_at: "now",
      customer_notify: true
    };

    // Get current subscription details from Razorpay to get accurate remaining_count
    const { getSubscription } = await import('@/lib/razorpay/services/subscription');
    const currentSubscription = await getSubscription(request.context.razorpaySubscriptionId);

    if (!currentSubscription.success || !currentSubscription.data) {
      return {
        success: false,
        subscriptionId: undefined,
        paymentRequired: false,
        message: 'Failed to get current subscription details',
        error: 'Could not fetch current subscription from Razorpay'
      };
    }

    const currentPeriod = request.context.currentPlanCycle;
    const newPeriod = request.planCycle;
    const currentRemainingCount = currentSubscription.data.remaining_count;

    console.log(`[SUBSCRIPTION_UPDATE] Current subscription details:`, {
      subscriptionId: request.context.razorpaySubscriptionId,
      currentPeriod,
      newPeriod,
      currentRemainingCount,
      currentPlanId: currentSubscription.data.plan_id,
      newPlanId: razorpayPlanId
    });

    // Always add remaining_count when updating subscription (as per Razorpay docs)
    if (currentPeriod && currentPeriod !== newPeriod) {
      // Period change: calculate equivalent remaining count
      if (currentPeriod === 'monthly' && newPeriod === 'yearly') {
        // Converting from monthly to yearly: divide by 12 and round up
        updateParams.remaining_count = Math.ceil(currentRemainingCount / 12);
        console.log(`[SUBSCRIPTION_UPDATE] Monthly → Yearly: ${currentRemainingCount} months → ${updateParams.remaining_count} years`);
      } else if (currentPeriod === 'yearly' && newPeriod === 'monthly') {
        // Converting from yearly to monthly: multiply by 12
        updateParams.remaining_count = currentRemainingCount * 12;
        console.log(`[SUBSCRIPTION_UPDATE] Yearly → Monthly: ${currentRemainingCount} years → ${updateParams.remaining_count} months`);
      }
    } else {
      // Same period: keep the same remaining count
      updateParams.remaining_count = currentRemainingCount;
      console.log(`[SUBSCRIPTION_UPDATE] Same period (${currentPeriod}): keeping remaining_count = ${updateParams.remaining_count}`);
    }

    // Update the subscription using Razorpay API
    const result = await updateSubscription(request.context.razorpaySubscriptionId, updateParams);

    return {
      success: result.success,
      subscriptionId: request.context.razorpaySubscriptionId, // Keep the same subscription ID
      paymentRequired: true, // Card updates require payment authorization
      message: result.success
        ? 'Subscription update initiated. Payment authorization required.'
        : 'Failed to update subscription',
      error: result.success ? undefined : (typeof result.error === 'string' ? result.error : 'Update failed'),
      data: result.success ? (result.data as unknown as Record<string, unknown>) : undefined
    };
  } catch (error) {
    console.error('Update subscription flow error:', error);
    return {
      success: false,
      subscriptionId: undefined,
      paymentRequired: false,
      message: 'Failed to update subscription',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Handle create and cancel flow (UPI/emandate)
 */
async function handleCreateAndCancelFlow(
  request: SubscriptionRequest,
  _decision: unknown
): Promise<SubscriptionFlowResult> {
  if (!request.context.razorpaySubscriptionId) {
    console.error('Missing razorpaySubscriptionId in context for create and cancel flow');
    return {
      success: false,
      subscriptionId: undefined,
      paymentRequired: false,
      message: 'Failed to process plan change: Current subscription ID not found.',
      error: 'Missing razorpaySubscriptionId in context'
    };
  }

  try {
    // Use server-side subscription creation that also handles cancelling the old one
    const { cancelAndCreateSubscription } = await import('@/lib/actions/subscription/create');
    const result = await cancelAndCreateSubscription(
      request.context.razorpaySubscriptionId,
      request.planId,
      request.planCycle
    );

    return {
      success: result.success,
      subscriptionId: (typeof result.data?.subscription_id === 'string' ? result.data.subscription_id : undefined),
      paymentRequired: true,
      message: result.success
        ? 'New subscription created. Your current subscription will be cancelled after activation.'
        : 'Failed to create new subscription',
      error: result.error,
      data: result.data
    };
  } catch (error) {
    console.error('Create and cancel flow error:', error);
    return {
      success: false,
      subscriptionId: undefined,
      paymentRequired: false,
      message: 'Failed to create new subscription',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Handle fresh subscription flow (new users or trial users)
 */
async function handleFreshSubscriptionFlow(
  request: SubscriptionRequest,
  decision: { requiresUpfrontPayment: boolean; paymentTiming: string }
): Promise<SubscriptionFlowResult> {
  try {
    // Use server-side subscription creation
    const { createSubscription } = await import('@/lib/actions/subscription/create');
    const result = await createSubscription(request.planId, request.planCycle);

    return {
      success: result.success,
      subscriptionId: (typeof result.data?.id === 'string' ? result.data.id : (typeof result.data?.subscription_id === 'string' ? result.data.subscription_id : undefined)),
      paymentRequired: decision.requiresUpfrontPayment,
      message: result.success
        ? (decision.paymentTiming === 'TRIAL_END'
            ? 'Subscription created. Payment will be processed when trial ends.'
            : 'Subscription created successfully.')
        : 'Failed to create subscription',
      error: result.error,
      data: result.data
    };
  } catch (error) {
    console.error('Fresh subscription flow error:', error);
    return {
      success: false,
      subscriptionId: undefined,
      paymentRequired: false,
      message: 'Failed to create subscription',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
