"use client";

import { UseFormReturn } from "react-hook-form";
import { BusinessCardData } from "../../schema";
import { Clock, Truck, Info } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { BusinessHoursEditor } from "./BusinessHoursEditor";

interface BusinessDetailsSectionProps {
  form: UseFormReturn<BusinessCardData>;
}

export default function BusinessDetailsSection({
  form,
}: BusinessDetailsSectionProps) {
  return (
    <div className="rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-6 mb-4 md:mb-6 transition-all duration-300 hover:shadow-lg">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800">
        <div className="p-2 rounded-lg bg-primary/10 text-primary self-start">
          <Clock className="w-4 sm:w-5 h-4 sm:h-5" />
        </div>
        <div className="flex-1">
          <h3 className="text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100">
            Business Details
          </h3>
          <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5">
            Add your business hours and delivery information
          </p>
        </div>
      </div>

      <div className="flex flex-col gap-4 sm:gap-6">
        {/* Business Hours */}
        <FormField
          control={form.control}
          name="business_hours"
          render={({ field }) => (
            <FormItem className="space-y-1 sm:space-y-2">
              <FormLabel className="text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5">
                <Clock className="h-3.5 w-3.5 text-primary" />
                Business Hours
              </FormLabel>
              <FormControl>
                <BusinessHoursEditor
                  value={field.value}
                  onChange={field.onChange}
                />
              </FormControl>
              <FormDescription className="text-xs text-neutral-500 dark:text-neutral-400 flex items-center gap-1 ml-1">
                <Info className="w-3 h-3" />
                Set your business hours to let customers know when you&apos;re
                open
              </FormDescription>
              <FormMessage className="text-xs text-red-500" />
            </FormItem>
          )}
        />

        {/* Delivery Info */}
        <FormField
          control={form.control}
          name="delivery_info"
          render={({ field }) => (
            <FormItem className="space-y-1 sm:space-y-2">
              <FormLabel className="text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5">
                <Truck className="h-3.5 w-3.5 text-primary" />
                Delivery Info
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <Truck className="absolute left-3 top-3 h-3.5 w-3.5 text-neutral-500 dark:text-neutral-400" />
                  <Textarea
                    placeholder="e.g., Free delivery within 5km, Delivery charges apply..."
                    {...field}
                    className="min-h-[80px] pl-10 rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm resize-none transition-all duration-200"
                    maxLength={100}
                  />
                  <div className="absolute right-2 bottom-2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500">
                    {field.value?.length || 0}/100
                  </div>
                </div>
              </FormControl>
              <FormDescription className="text-xs text-neutral-500 dark:text-neutral-400 flex items-center gap-1 ml-1">
                <Info className="w-3 h-3" />
                Delivery details shown on your card
              </FormDescription>
              <FormMessage className="text-xs text-red-500" />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}
