"use client";

import { motion } from "framer-motion";
import { LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface EnhancedMetricCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  description: string;
  color: "rose" | "blue" | "amber" | "green" | "purple" | "indigo";
  isUpdated?: boolean;
  suffix?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

export default function EnhancedMetricCard({
  title,
  value,
  icon: Icon,
  description,
  color,
  isUpdated = false,
  suffix,
  trend,
}: EnhancedMetricCardProps) {
  // Define color variants
  const colorVariants = {
    rose: {
      bgLight: "bg-rose-100",
      bgDark: "dark:bg-rose-900/30",
      textLight: "text-rose-600",
      textDark: "dark:text-rose-400",
      borderHover: "group-hover:border-rose-300 dark:group-hover:border-rose-700",
      glowColor: "rgba(244, 63, 94, 0.4)",
    },
    blue: {
      bgLight: "bg-blue-100",
      bgDark: "dark:bg-blue-900/30",
      textLight: "text-blue-600",
      textDark: "dark:text-blue-400",
      borderHover: "group-hover:border-blue-300 dark:group-hover:border-blue-700",
      glowColor: "rgba(59, 130, 246, 0.4)",
    },
    amber: {
      bgLight: "bg-amber-100",
      bgDark: "dark:bg-amber-900/30",
      textLight: "text-amber-600",
      textDark: "dark:text-amber-400",
      borderHover: "group-hover:border-amber-300 dark:group-hover:border-amber-700",
      glowColor: "rgba(245, 158, 11, 0.4)",
    },
    green: {
      bgLight: "bg-green-100",
      bgDark: "dark:bg-green-900/30",
      textLight: "text-green-600",
      textDark: "dark:text-green-400",
      borderHover: "group-hover:border-green-300 dark:group-hover:border-green-700",
      glowColor: "rgba(34, 197, 94, 0.4)",
    },
    purple: {
      bgLight: "bg-purple-100",
      bgDark: "dark:bg-purple-900/30",
      textLight: "text-purple-600",
      textDark: "dark:text-purple-400",
      borderHover: "group-hover:border-purple-300 dark:group-hover:border-purple-700",
      glowColor: "rgba(168, 85, 247, 0.4)",
    },
    indigo: {
      bgLight: "bg-indigo-100",
      bgDark: "dark:bg-indigo-900/30",
      textLight: "text-indigo-600",
      textDark: "dark:text-indigo-400",
      borderHover: "group-hover:border-indigo-300 dark:group-hover:border-indigo-700",
      glowColor: "rgba(99, 102, 241, 0.4)",
    },
  };

  const selectedColor = colorVariants[color];

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24,
      },
    },
  };

  const counterVariants = {
    initial: { scale: 1 },
    update: {
      scale: [1, 1.1, 1],
      transition: { duration: 0.3 },
    },
  };

  return (
    <motion.div
      variants={containerVariants}
    >
      <div
        className={cn(
          "rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 transition-all duration-300 hover:shadow-lg overflow-hidden group",
          selectedColor.borderHover
        )}
      >
      <div className="flex items-center gap-2 mb-2">
        <div className={cn("p-1.5 sm:p-2 rounded-lg self-start", selectedColor.bgLight, selectedColor.bgDark, selectedColor.textLight, selectedColor.textDark)}>
          <Icon className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
        </div>
        <h3 className="text-sm sm:text-base font-semibold text-neutral-800 dark:text-neutral-100 truncate">
          {title}
        </h3>
      </div>
      <div className="px-1 pb-2">
        <div className="flex items-end gap-1">
          <motion.div
            variants={counterVariants}
            initial="initial"
            animate={isUpdated ? "update" : "initial"}
          >
            <div className="text-xl sm:text-2xl font-bold">
              {/* Add glow effect to the value */}
              <span className="relative">
                <span className="relative z-10">{value}</span>
                {suffix && <span className="text-sm text-muted-foreground ml-1">{suffix}</span>}
                <div
                  className="absolute inset-0 blur-sm opacity-50 z-0"
                  style={{ backgroundColor: selectedColor.glowColor }}
                />
              </span>
            </div>
          </motion.div>

          {/* Trend indicator */}
          {trend && (
            <motion.div
              initial={{ opacity: 0, y: 5 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <div
                className={cn(
                  "text-xs font-medium mb-1 flex items-center",
                  trend.isPositive ? "text-green-500 dark:text-green-400" : "text-rose-500 dark:text-rose-400"
                )}
              >
                {trend.isPositive ? "↑" : "↓"} {Math.abs(trend.value)}%
              </div>
            </motion.div>
          )}
        </div>
        <p className="text-xs sm:text-sm text-neutral-500 dark:text-neutral-400 mt-1">
          {description}
        </p>
      </div>

      {/* Animated border effect */}
      <div
        className="absolute bottom-0 left-0 h-1 bg-gradient-to-r"
        style={{
          backgroundImage: `linear-gradient(to right, transparent, ${selectedColor.glowColor}, transparent)`,
          width: "100%",
        }}
      />
      </div>
    </motion.div>
  );
}
