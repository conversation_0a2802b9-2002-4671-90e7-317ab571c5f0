"use client";

import { useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { <PERSON><PERSON>, Type, Eye, EyeOff, Image } from "lucide-react";
import { BusinessCardData } from "../../schema";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { compressImageModerateClient } from "@/lib/utils/client-image-compression";

import { useTheme } from "next-themes";
import ThemeSpecificHeaderUpload from "./ThemeSpecificHeaderUpload";

interface CustomBrandingSectionProps {
  form: UseFormReturn<BusinessCardData>;
  currentUserPlan: "basic" | "growth" | "pro" | "enterprise" | "trial" | null;
}

export default function CustomBrandingSection({
  form,
  currentUserPlan,
}: CustomBrandingSectionProps) {
  // Check if user has access to custom branding
  const hasCustomBrandingAccess = currentUserPlan === "pro" || currentUserPlan === "enterprise";

  // Theme hook
  const { resolvedTheme } = useTheme();

  // State for theme-specific header uploads
  const [isDraggingLightHeader, setIsDraggingLightHeader] = useState(false);
  const [isDraggingDarkHeader, setIsDraggingDarkHeader] = useState(false);

  // Handle custom header text change - automatically toggle hide branding
  const handleHeaderTextChange = (value: string) => {
    // If user types something, automatically enable hide branding
    if (value.trim()) {
      form.setValue("custom_branding.hide_dukancard_branding", true);
    }
    // If user clears the text, disable hide branding
    else {
      form.setValue("custom_branding.hide_dukancard_branding", false);
    }
  };

  // Handle hide branding toggle change
  const handleHideBrandingToggle = (checked: boolean) => {
    // If turning off hide branding, clear any validation errors
    if (!checked) {
      // Clear validation errors for custom_header_text when toggle is off
      form.clearErrors("custom_branding.custom_header_text");
      // Trigger validation to update the form state
      form.trigger("custom_branding");
    }
  };



  // Theme-specific header upload handlers - store files for upload on save
  const handleThemeSpecificHeaderFileSelect = async (file: File | null, theme: 'light' | 'dark') => {
    if (!file) return;

    // Frontend validation for file type
    if (!file.type.startsWith("image/")) {
      toast.error("Please upload a valid image file");
      return;
    }

    // Frontend validation for file size (5MB limit)
    const maxSizeBytes = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSizeBytes) {
      const fileSizeMB = (file.size / (1024 * 1024)).toFixed(1);
      toast.error(`Image size (${fileSizeMB}MB) is too large. Please choose an image smaller than 5MB for optimal performance.`);
      return;
    }

    try {
      // Compress image on client-side first
      const compressionResult = await compressImageModerateClient(file, {
        maxDimension: 400, // Smaller size for header images
        targetSizeKB: 150  // Target 150KB max for header images
      });

      // Convert compressed blob back to file
      const compressedFile = new File([compressionResult.blob], file.name, {
        type: compressionResult.blob.type
      });

      // Create a local preview URL for immediate feedback
      const previewUrl = URL.createObjectURL(compressedFile);

      // Store the compressed file and preview URL in form state for upload on save
      if (theme === 'light') {
        form.setValue("custom_branding.custom_header_image_light_url", previewUrl, { shouldDirty: true });
        form.setValue("custom_branding.pending_light_header_file", compressedFile, { shouldDirty: true });
      } else {
        form.setValue("custom_branding.custom_header_image_dark_url", previewUrl, { shouldDirty: true });
        form.setValue("custom_branding.pending_dark_header_file", compressedFile, { shouldDirty: true });
      }

      form.setValue("custom_branding.hide_dukancard_branding", true, { shouldDirty: true });
      toast.success(`${theme === 'light' ? 'Light' : 'Dark'} theme header image compressed and ready. Click "Save Changes" to upload.`);
    } catch (error) {
      console.error("Image compression failed:", error);
      toast.error("Failed to process image. Please try a different image.");
    }
  };

  const handleThemeSpecificHeaderDelete = (theme: 'light' | 'dark') => {
    // Only update form state, actual deletion will happen on save
    if (theme === 'light') {
      form.setValue("custom_branding.custom_header_image_light_url", "", { shouldDirty: true });
      form.setValue("custom_branding.pending_light_header_file", null, { shouldDirty: true });
    } else {
      form.setValue("custom_branding.custom_header_image_dark_url", "", { shouldDirty: true });
      form.setValue("custom_branding.pending_dark_header_file", null, { shouldDirty: true });
    }
    toast.success(`${theme === 'light' ? 'Light' : 'Dark'} theme header image will be removed when you save changes`);
  };

  // Theme-specific drag and drop handlers
  const handleThemeSpecificHeaderDrop = (e: React.DragEvent, theme: 'light' | 'dark') => {
    e.preventDefault();
    const setDragging = theme === 'light' ? setIsDraggingLightHeader : setIsDraggingDarkHeader;
    setDragging(false);
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleThemeSpecificHeaderFileSelect(files[0], theme);
    }
  };

  const handleThemeSpecificHeaderDragOver = (e: React.DragEvent, theme: 'light' | 'dark') => {
    e.preventDefault();
    const setDragging = theme === 'light' ? setIsDraggingLightHeader : setIsDraggingDarkHeader;
    setDragging(true);
  };

  const handleThemeSpecificHeaderDragLeave = (e: React.DragEvent, theme: 'light' | 'dark') => {
    e.preventDefault();
    const setDragging = theme === 'light' ? setIsDraggingLightHeader : setIsDraggingDarkHeader;
    setDragging(false);
  };

  if (!hasCustomBrandingAccess) {
    return (
      <div className="space-y-4 p-4 border border-amber-200 dark:border-amber-800 rounded-lg bg-amber-50 dark:bg-amber-950/20">
        <div className="flex items-center gap-2">
          <Palette className="h-5 w-5 text-amber-600 dark:text-amber-400" />
          <h3 className="text-lg font-semibold text-amber-800 dark:text-amber-200">
            Custom Branding
          </h3>
          <Badge variant="secondary" className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200">
            Pro Feature
          </Badge>
        </div>
        <p className="text-sm text-amber-700 dark:text-amber-300">
          Upgrade to Pro or Enterprise plan to access custom branding features including custom logos,
          watermarks, colors, and the ability to hide Dukancard branding.
        </p>
        <Button variant="outline" className="border-amber-300 text-amber-700 hover:bg-amber-100 dark:border-amber-700 dark:text-amber-300 dark:hover:bg-amber-900/20">
          Upgrade to Pro
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Palette className="h-5 w-5 text-primary" />
        <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-200">
          Custom Branding
        </h3>
        <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
          {currentUserPlan?.toUpperCase()}
        </Badge>
      </div>

      <div className="space-y-6">
        {/* Hide Dukancard Branding Toggle */}
        <FormField
          control={form.control}
          name="custom_branding.hide_dukancard_branding"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base font-medium flex items-center gap-2">
                  {field.value ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  Hide Dukancard Branding
                </FormLabel>
                <p className="text-sm text-muted-foreground">
                  Remove Dukancard branding from your business card
                </p>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={(checked) => {
                    field.onChange(checked);
                    handleHideBrandingToggle(checked);
                  }}
                />
              </FormControl>
            </FormItem>
          )}
        />

        <Separator />

        {/* Custom Header Text */}
        <FormField
          control={form.control}
          name="custom_branding.custom_header_text"
          render={({ field }) => (
            <FormItem className="space-y-2">
              <FormLabel className="text-sm font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5">
                <Type className="h-4 w-4 text-primary" />
                Custom Header Text
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  value={field.value || ""}
                  onChange={(e) => {
                    field.onChange(e.target.value);
                    handleHeaderTextChange(e.target.value);
                  }}
                  placeholder="e.g., Powered by YourBrand"
                  maxLength={50}
                />
              </FormControl>
              <p className="text-xs text-muted-foreground">
                Custom text to display in the header instead of Dukancard branding (max 50 characters). Required when &quot;Hide Dukancard Branding&quot; is enabled.
              </p>
              <FormMessage />
            </FormItem>
          )}
        />

        <Separator />

        {/* Theme-Specific Custom Header Images */}
        <div className="space-y-4">
          <div className="space-y-2">
            <FormLabel className="text-sm font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5">
              <Image className="h-4 w-4 text-primary" aria-label="Theme specific header" />
              Theme-Specific Header Images (Alternative to Text)
            </FormLabel>
            <p className="text-xs text-muted-foreground">
              Upload custom images for light and dark themes. Images will automatically switch based on the user&apos;s theme preference. PNG format recommended for best quality.
            </p>
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <span className="flex items-center gap-1">
                <span className={`w-2 h-2 rounded-full ${resolvedTheme === 'light' ? 'bg-yellow-500' : 'bg-gray-400'}`}></span>
                Current: {resolvedTheme === 'light' ? 'Light' : resolvedTheme === 'dark' ? 'Dark' : 'System'}
              </span>
            </div>
          </div>

          {/* Light and Dark Theme Upload Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ThemeSpecificHeaderUpload
              theme="light"
              imageUrl={form.watch("custom_branding.custom_header_image_light_url")}
              isUploading={false}
              isDeleting={false}
              isDragging={isDraggingLightHeader}
              onFileSelect={handleThemeSpecificHeaderFileSelect}
              onDelete={handleThemeSpecificHeaderDelete}
              onDrop={handleThemeSpecificHeaderDrop}
              onDragOver={handleThemeSpecificHeaderDragOver}
              onDragLeave={handleThemeSpecificHeaderDragLeave}
            />

            <ThemeSpecificHeaderUpload
              theme="dark"
              imageUrl={form.watch("custom_branding.custom_header_image_dark_url")}
              isUploading={false}
              isDeleting={false}
              isDragging={isDraggingDarkHeader}
              onFileSelect={handleThemeSpecificHeaderFileSelect}
              onDelete={handleThemeSpecificHeaderDelete}
              onDrop={handleThemeSpecificHeaderDrop}
              onDragOver={handleThemeSpecificHeaderDragOver}
              onDragLeave={handleThemeSpecificHeaderDragLeave}
            />
          </div>
        </div>

        <Separator />

        {/* Theme Color Note */}
        <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center flex-shrink-0 mt-0.5">
              <Palette className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h4 className="text-sm font-semibold text-blue-900 dark:text-blue-100 mb-1">
                Theme Color Customization
              </h4>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                Customize your business card&apos;s theme color in the <strong>Appearance</strong> section above.
                Pro and Enterprise users can choose any custom color, while other plans use the default gold theme.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
