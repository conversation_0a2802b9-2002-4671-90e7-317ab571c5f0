/**
 * CENTRALIZED SUBSCRIPTION FLOW MANAGER
 * 
 * This is the single source of truth for all subscription logic.
 * It implements professional enterprise-level logic for handling:
 * - Fresh subscriptions (post-trial, no active subscription)
 * - Plan switches (active subscription with different payment methods)
 * - Trial to paid transitions
 * 
 * BUSINESS RULES:
 * 1. Post-trial + no razorpay_subscription_id = UPFRONT_PAYMENT (take money first, update DB after active)
 * 2. Active subscription + card payment = UPDATE_SUBSCRIPTION (update existing subscription)
 * 3. Active subscription + UPI/emandate/unknown/null = CREATE_AND_CANCEL (create new, cancel old after active)
 */

import {
  SubscriptionRequest,
  SubscriptionFlowDecision,
  PLAN_IDS
} from './types';

export class SubscriptionFlowManager {
  /**
   * MASTER DECISION FUNCTION
   * Determines the correct subscription flow based on current state
   */
  static determineSubscriptionFlow(request: SubscriptionRequest): SubscriptionFlowDecision {
    const { planId, planCycle, context, paymentMethod } = request;
    const {
      trialEndDate,
      razorpaySubscriptionId,
      lastPaymentMethod,
      subscriptionStatus,
      hasActiveSubscription
    } = context;

    // Check if user is post-trial
    const isPostTrial = this.isPostTrial(trialEndDate);

    // Check if user has active Razorpay subscription
    const hasRazorpaySubscription = Boolean(razorpaySubscriptionId);

    // Determine payment method type - use provided paymentMethod first, fallback to database value
    const effectivePaymentMethod = paymentMethod || lastPaymentMethod;
    const paymentMethodType = this.categorizePaymentMethod(effectivePaymentMethod);

    console.log(`[SUBSCRIPTION_FLOW] Payment method decision:`, {
      providedPaymentMethod: paymentMethod,
      databasePaymentMethod: lastPaymentMethod,
      effectivePaymentMethod,
      paymentMethodType
    });

    // BUSINESS RULE 1: Post-trial with no active subscription
    if (isPostTrial && !hasRazorpaySubscription) {
      return {
        flowType: 'UPFRONT_PAYMENT',
        requiresUpfrontPayment: true,
        shouldCancelExisting: false,
        shouldUpdateExisting: false,
        paymentTiming: 'IMMEDIATE',
        reason: 'Post-trial user with no active subscription - requires upfront payment'
      };
    }

    // BUSINESS RULE 2: Active subscription with card payment
    if (hasRazorpaySubscription && paymentMethodType === 'CARD') {
      return {
        flowType: 'UPDATE_SUBSCRIPTION',
        requiresUpfrontPayment: false,
        shouldCancelExisting: false,
        shouldUpdateExisting: true,
        paymentTiming: 'IMMEDIATE',
        reason: 'Active subscription with card payment - update existing subscription'
      };
    }

    // BUSINESS RULE 3: Active subscription with UPI/emandate/unknown/null
    if (hasRazorpaySubscription && paymentMethodType !== 'CARD') {
      return {
        flowType: 'CREATE_AND_CANCEL',
        requiresUpfrontPayment: true,
        shouldCancelExisting: true,
        shouldUpdateExisting: false,
        paymentTiming: 'IMMEDIATE',
        reason: 'Active subscription with UPI/emandate/unknown payment - create new and cancel old'
      };
    }

    // BUSINESS RULE 4: Fresh subscription (trial or no subscription)
    return {
      flowType: 'FRESH_SUBSCRIPTION',
      requiresUpfrontPayment: !this.isOnTrial(trialEndDate),
      shouldCancelExisting: false,
      shouldUpdateExisting: false,
      paymentTiming: this.isOnTrial(trialEndDate) ? 'TRIAL_END' : 'IMMEDIATE',
      reason: 'Fresh subscription - new user or trial user'
    };
  }

  /**
   * Check if user is post-trial (trial has ended)
   */
  private static isPostTrial(trialEndDate?: string | null): boolean {
    if (!trialEndDate) return false;
    return new Date(trialEndDate) <= new Date();
  }

  /**
   * Check if user is currently on trial
   */
  private static isOnTrial(trialEndDate?: string | null): boolean {
    if (!trialEndDate) return false;
    return new Date(trialEndDate) > new Date();
  }

  /**
   * Categorize payment method for decision making
   */
  private static categorizePaymentMethod(paymentMethod?: string | null): 'CARD' | 'UPI_EMANDATE' | 'UNKNOWN' {
    if (!paymentMethod) return 'UNKNOWN';

    const method = paymentMethod.toLowerCase();

    if (method === 'card') {
      return 'CARD';
    }

    if (method === 'upi' || method === 'emandate' || method === 'enach') {
      return 'UPI_EMANDATE';
    }

    return 'UNKNOWN';
  }

  /**
   * Enhanced payment method detection with Razorpay API fallback
   * This method should be used for plan switching scenarios to ensure accuracy
   */
  static async getActualPaymentMethod(
    razorpaySubscriptionId: string,
    fallbackMethod?: string | null
  ): Promise<'CARD' | 'UPI_EMANDATE' | 'UNKNOWN'> {
    try {
      // Import Razorpay service
      const { getSubscription } = await import('@/lib/razorpay/services/subscription');

      // Fetch subscription details from Razorpay
      const result = await getSubscription(razorpaySubscriptionId);

      if (result.success && result.data) {
        // Try to get payment method from subscription notes or recent payments
        const subscription = result.data;

        // Check if payment method is stored in notes
        if (subscription.notes?.payment_method) {
          return this.categorizePaymentMethod(subscription.notes.payment_method);
        }

        // If we have a last payment ID, fetch payment details
        if (subscription.notes?.last_payment_id) {
          const { getPayment } = await import('@/lib/razorpay/services/payment');
          const paymentResult = await getPayment(subscription.notes.last_payment_id);

          if (paymentResult.success && paymentResult.data) {
            return this.categorizePaymentMethod(paymentResult.data.method);
          }
        }
      }

      console.warn('[SUBSCRIPTION_FLOW] Could not fetch payment method from Razorpay, using fallback');
      return this.categorizePaymentMethod(fallbackMethod);

    } catch (error) {
      console.error('[SUBSCRIPTION_FLOW] Error fetching payment method from Razorpay:', error);
      return this.categorizePaymentMethod(fallbackMethod);
    }
  }

  /**
   * Validate subscription request
   */
  static validateRequest(request: SubscriptionRequest): { valid: boolean; error?: string } {
    const { planId, planCycle, context } = request;

    // Validate plan ID
    if (!Object.values(PLAN_IDS).includes(planId as 'free' | 'basic' | 'growth' | 'pro' | 'enterprise')) {
      return { valid: false, error: 'Invalid plan ID' };
    }

    // Validate plan cycle
    if (!['monthly', 'yearly'].includes(planCycle)) {
      return { valid: false, error: 'Invalid plan cycle' };
    }

    // Validate context
    if (!context.userId) {
      return { valid: false, error: 'User ID is required' };
    }

    return { valid: true };
  }

  /**
   * Get user-friendly flow description
   */
  static getFlowDescription(decision: SubscriptionFlowDecision): string {
    switch (decision.flowType) {
      case 'UPFRONT_PAYMENT':
        return 'Payment will be processed immediately and subscription activated upon successful payment.';
      case 'UPDATE_SUBSCRIPTION':
        return 'Your existing subscription will be updated with the new plan.';
      case 'CREATE_AND_CANCEL':
        return 'A new subscription will be created and your current subscription will be cancelled after activation.';
      case 'FRESH_SUBSCRIPTION':
        return decision.paymentTiming === 'TRIAL_END' 
          ? 'Subscription will be created and payment will be processed when your trial ends.'
          : 'New subscription will be created and activated immediately.';
      default:
        return 'Subscription will be processed according to your current plan status.';
    }
  }
}
